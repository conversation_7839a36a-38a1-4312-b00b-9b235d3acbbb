<?xml version="1.0" encoding="utf-8"?>
<!-- 主播card作为RecyclerView的第一个item -->
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/broadcasterCard"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="18dp"
    android:layout_marginTop="8dp"
    android:layout_marginBottom="8dp"
    android:minHeight="146dp"
    app:cardCornerRadius="10dp"
    app:cardElevation="4dp"
    app:cardMaxElevation="4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="12dp">

        <!-- 年龄、国家、语言 -->
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/age"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawablePadding="1dp"
            android:gravity="center"
            android:paddingStart="4dp"
            android:paddingTop="2dp"
            android:paddingEnd="4dp"
            android:paddingBottom="2dp"
            android:textColor="@color/age_color"
            android:textSize="10sp"
            app:drawableStartCompat="@drawable/chat_girl"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="@drawable/bg_btn_rounded_black"
            tools:text="26" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/country"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:gravity="center"
            android:paddingLeft="5dp"
            android:paddingTop="2dp"
            android:paddingRight="5dp"
            android:paddingBottom="2dp"
            android:textColor="@color/chat_country"
            android:textSize="10sp"
            app:layout_constraintStart_toEndOf="@id/age"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="@drawable/bg_btn_rounded_black"
            tools:text="India" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/language"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:drawablePadding="1dp"
            android:gravity="center"
            android:paddingLeft="4dp"
            android:paddingTop="2dp"
            android:paddingRight="4dp"
            android:paddingBottom="2dp"
            android:textColor="@color/chat_lang"
            android:textSize="10sp"
            app:drawableStartCompat="@drawable/chat_language"
            app:layout_constraintStart_toEndOf="@id/country"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="@drawable/bg_btn_rounded_black"
            tools:text="English" />

        <!--    价格        -->
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/icon_price"
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:src="@drawable/coin"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="@id/price"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/price" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="3dp"
            android:layout_marginTop="9dp"
            android:background="@color/transparent"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="10sp"
            app:layout_constraintStart_toEndOf="@id/icon_price"
            app:layout_constraintTop_toBottomOf="@id/age"
            tools:text="200/min" />

        <!--      divide      -->
        <View
            android:id="@+id/divide_line"
            android:layout_width="0dp"
            android:layout_height="2dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/chat_card_divide_line"
            android:layerType="software"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/price" />

        <!--     相册       -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvPhotoAlbum"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="11dp"
            android:clipToPadding="false"
            android:orientation="horizontal"
            android:overScrollMode="never"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divide_line"
            tools:itemCount="5"
            tools:listitem="@layout/item_photo_album" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
