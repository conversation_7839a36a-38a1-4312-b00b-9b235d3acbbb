<!-- item_banner_header.xml -->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">


    <com.score.callmetest.ui.widget.ViewPager2FrameLayoutHost
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" >

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/bannerViewPager"
            android:layout_width="match_parent"
            android:layout_height="200dp"/>
    </com.score.callmetest.ui.widget.ViewPager2FrameLayoutHost>

    <LinearLayout
        android:id="@+id/dot_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
