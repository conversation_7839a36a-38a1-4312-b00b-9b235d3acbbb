<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.Callme" parent="Theme.AppCompat.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@android:color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
    </style>
    
    <!-- No Action Bar theme for login and web view activities -->
    <style name="Theme.Callme.NoActionBar" parent="Theme.Callme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>

    <!-- 沉浸式状态栏主题 -->
    <style name="Theme.Callme.Immersive" parent="Theme.Callme.NoActionBar">
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar">true</item>
        <!-- 设置全屏布局 -->
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <!-- 隐藏状态栏 -->
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="CircleImageView" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <style name="SettingItemTitle">
        <item name="android:textColor">#FF000000</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">14sp</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:typeface">sans</item>
    </style>

    <style name="SettingItemTip">
        <item name="android:textColor">#FFAEAEAE</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">14sp</item>
        <item name="android:includeFontPadding">false</item>
    </style>
    <style name="SettingItemButton">
        <item name="android:textColor">#FFAEAEAE</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">13sp</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <!-- 自定义Tab样式 -->
    <style name="CustomTabStyle" parent="Widget.MaterialComponents.TabLayout">
        <item name="tabTextAppearance">@style/CustomTabTextAppearance</item>
        <item name="tabSelectedTextAppearance">@style/CustomTabSelectedTextAppearance</item>
        <item name="tabIndicator">@drawable/tab_indicator</item>
        <item name="tabIndicatorFullWidth">false</item>
        <item name="tabIndicatorGravity">bottom</item>
    </style>

    <!-- 未选中状态的Tab文字样式 -->
    <style name="CustomTabTextAppearance" parent="TextAppearance.MaterialComponents.Button">
        <item name="android:textSize">@dimen/tab_text_size_unselected</item>
        <item name="android:textColor">@android:color/black</item>
        <item name="android:textStyle">normal</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <!-- 选中状态的Tab文字样式 -->
    <style name="CustomTabSelectedTextAppearance" parent="TextAppearance.MaterialComponents.Button">
        <item name="android:textSize">@dimen/tab_text_size_selected</item>
        <item name="android:textColor">@android:color/black</item>
        <item name="android:textStyle">bold</item>
        <item name="android:includeFontPadding">false</item>
    </style>
</resources>