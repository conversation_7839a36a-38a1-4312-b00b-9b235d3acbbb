package com.score.callmetest.manager

import DateUtils
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.score.callmetest.network.FlashChatRequest
import com.score.callmetest.network.FlashChatResponse
import com.score.callmetest.network.MatchCancelReq
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.UserInfo
import com.score.callmetest.ui.main.MainActivity
import com.score.callmetest.ui.widget.FlashChatDialog
import com.score.callmetest.ui.widget.FlashChatWelcomeDialog
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ThreadUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.UUID

object FlashChatManager {

    const val FLASH_DIALOG_SHOW_TIMES = "flash_dialog_show_times"
    const val FLASH_DIALOG_SHOW_WELCOME = "flash_dialog_show_welcome"

    /**
     * 免费匹配次数
     */
    var matchFreeTimes = 0
    var hasShowDialog = false
    var welcomeDialogShowing = false

    var showDialogHandler: Handler? = null
    var showDialogRunnable: Runnable? = null

    fun init() {
        StrategyManager.strategyConfig?.flashChatConfig?.residueFreeCallTimes.let { times ->
            matchFreeTimes = times!!
        }
        showDialogHandler = Handler(Looper.getMainLooper())
        showDialogRunnable = Runnable {
            if (hasShowDialog) {
                return@Runnable
            }
            ActivityUtils.getTopActivity()?.let { activity ->
                showMatchDialog(activity as AppCompatActivity)
            }
        }

        // 延迟1s再弹
        ThreadUtils.runOnMainDelayed(1000) {
            ActivityUtils.getTopActivity()?.let { activity ->
                showWelcomeDialog(activity as AppCompatActivity)
            }
        }
    }

    fun canShowFlashChat(): Boolean {
        return StrategyManager.strategyConfig?.flashChatConfig?.isSwitch == true
                && !StrategyManager.isReviewPkg()
    }

    fun isFreeCall(): Boolean {
        return StrategyManager.strategyConfig?.flashChatConfig?.isFreeCall == true
    }

    fun getMatchCoins(): Int {
        return StrategyManager.strategyConfig?.genderMatchCoin?.femaleCoins ?: 0
    }

    /**
     * 20s后弹窗任务
     */
    fun startShowDialogTimer() {
        if (!canShowFlashChat()) {
            return
        }
        if (hasShowDialog) {
            return
        }
        ActivityUtils.getTopActivity()?.let { activity ->
            if (activity is MainActivity &&
                (activity.getCurrentTabIndex() == 0 || activity.getCurrentTabIndex() == 1)
            ) {
                showDialogRunnable?.let { runnable ->
                    showDialogHandler?.removeCallbacks(runnable)
                    showDialogHandler?.postDelayed(runnable, 20000)
                }
                Log.d("FlashChatManager", "startShowDialogTimer")
            }
        }
    }

    /**
     * 停止弹窗计时任务
     */
    fun stopShowDialogTimer() {
        if (!canShowFlashChat()) {
            return
        }
        showDialogRunnable?.let { runnable ->
            showDialogHandler?.removeCallbacks(runnable)
        }
        Log.d("FlashChatManager", "stopShowDialogTimer")
    }

    /**
     * 匹配弹窗
     * 弹窗的触发时机：
     * 若用户在app【主播墙、message】列表 停留超过20秒，未有发起呼叫/接听、拉起充值弹窗行为时触发，
     * 每个弹窗每天最多出现3次（每次冷启动出现1次）。
     */
    fun showMatchDialog(
        activity: AppCompatActivity,
    ) {
        if (!canShowFlashChat()) {
            return
        }
        if (hasShowDialog) {
            return
        }
        if (welcomeDialogShowing) {
            return
        }

        var canShowDialog = false

        val date = DateUtils.getCurrentDate()
        val oldValue = SharePreferenceUtil.getString(FLASH_DIALOG_SHOW_TIMES)
        if (oldValue.isNullOrEmpty()) {
            SharePreferenceUtil.putString(FLASH_DIALOG_SHOW_TIMES, "${date}_1")
            canShowDialog = true
        } else {
            val oldDate = oldValue.split("_")[0]
            val oldTimes = oldValue.split("_")[1].toInt()
            if (oldDate == date) {
                // 同一天只出现3次
                if (oldTimes < 3) {
                    SharePreferenceUtil.putString(
                        FLASH_DIALOG_SHOW_TIMES,
                        "${date}_${oldTimes + 1}"
                    )
                    canShowDialog = true
                } else {
                    canShowDialog = false
                }
            } else {
                SharePreferenceUtil.putString(FLASH_DIALOG_SHOW_TIMES, "${date}_1")
                canShowDialog = true
            }
        }

        if (canShowDialog) {
            val dialog = FlashChatDialog(
                activity = activity,
                isFree = isFreeCall() && matchFreeTimes > 0,
                unitPrice = getMatchCoins(),
            )
            hasShowDialog = true
            dialog.show()
        }
    }

    /**
     * 欢迎弹窗
     */
    fun showWelcomeDialog(
        activity: AppCompatActivity,
    ) {
        if (!canShowFlashChat()) {
            return
        }
        if (matchFreeTimes <= 0) {
            return
        }

        val hasShowWelcomeDialog = SharePreferenceUtil.getBoolean(FLASH_DIALOG_SHOW_WELCOME, false)
        if (hasShowWelcomeDialog) {
            return
        }
        val dialog = FlashChatWelcomeDialog(
            activity = activity,
        ).apply {
            setOnDismissListener {
                welcomeDialogShowing = false
                startShowDialogTimer()
            }
        }
        dialog.show()
        welcomeDialogShowing = true
        SharePreferenceUtil.putBoolean(FLASH_DIALOG_SHOW_WELCOME, true)
        stopShowDialogTimer()
    }

    fun loadRandomBroadcaster(
        scope: CoroutineScope,
    ) {
        getRandomBroadcasterPostV2(
            scope = scope,
            callback = { userinfo ->
                if (userinfo != null) {

                }
            }
        )
    }

    fun getRandomBroadcasterPostV2(
        scope: CoroutineScope,
        callback: (UserInfo?) -> Unit,
    ) {
        scope.launch(Dispatchers.IO) {
            try {
                val resp = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.getRandomBroadcasterPostV2()
                }
                val data = if (resp is NetworkResult.Success) {
                    resp.data
                } else null
                scope.launch(Dispatchers.Main) {
                    callback.invoke(data)
                }
            } catch (e: Exception) {
                scope.launch(Dispatchers.Main) {
                    callback.invoke(null)
                }
            }
        }
    }

    fun getBatchId(): String {
        val time = System.currentTimeMillis().toString();
        return time.substring(time.length - 3) + ((Math.random() * 9 + 1) * 100000).toInt()
    }

    var lastSessionId: String? = null
    fun flashChat(
        scope: CoroutineScope,
        callback: (FlashChatResponse?) -> Unit,
    ) {
        scope.launch {
            try {
                val resp = withContext(Dispatchers.IO) {
                    lastSessionId = UUID.randomUUID().toString()
                    RetrofitUtils.dataRepository.flashChat(
                        FlashChatRequest(
                            batchId = getBatchId(),
                            clientSessionId = lastSessionId!!
                        )
                    )
                }
                val data = if (resp is NetworkResult.Success) {
                    resp.data
                } else null
                scope.launch(Dispatchers.Main) {
                    callback.invoke(data)
                }
            } catch (e: Exception) {
                scope.launch(Dispatchers.Main) {
                    callback.invoke(null)
                }
            }
        }
    }

    fun matchCancel(
        scope: CoroutineScope,
        callback: (Boolean) -> Unit,
    ) {
        scope.launch(Dispatchers.IO) {
            try {
                if (lastSessionId.isNullOrEmpty()) {
                    scope.launch(Dispatchers.Main) {
                        callback.invoke(false)
                    }
                    return@launch
                }
                val resp = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.matchCancel(
                        MatchCancelReq(
                            clientSessionId = lastSessionId!!
                        )
                    )
                }
                val data = if (resp is NetworkResult.Success) {
                    resp.data
                } else false
                scope.launch(Dispatchers.Main) {
                    callback.invoke(data!!)
                }
            } catch (e: Exception) {
                scope.launch(Dispatchers.Main) {
                    callback.invoke(false)
                }
            }
        }
    }
} 