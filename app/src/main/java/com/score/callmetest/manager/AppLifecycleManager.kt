package com.score.callmetest.manager

import android.app.Activity
import android.app.Application
import android.os.Bundle
import com.score.callmetest.entity.CustomEvents
import com.score.callmetest.network.RetrofitUtils // 假设这些导入是必要的
import com.score.callmetest.network.UserModeSwitchReq // 假设这些导入是必要的
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.ThreadUtils // 假设这些导入是必要的
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference
import java.util.concurrent.CopyOnWriteArrayList

object AppLifecycleManager {
    private const val TAG = "AppLifecycleManager"
    private var currentActivityRef: WeakReference<Activity>? = null
    private var startedActivityCount = 0
    private var isAppInForeground = false // 初始化为 false
    private val extraCallbacks = CopyOnWriteArrayList<Application.ActivityLifecycleCallbacks>()

    // 前台切换监听器
    private var foregroundListener: (() -> Unit)? = null
    // 后台切换监听器 (新增)
    private var backgroundListener: (() -> Unit)? = null

    val activityLifecycleCallbacks = object : Application.ActivityLifecycleCallbacks {
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            // 在 Activity 创建时，可以更新 currentActivityRef，但这通常在 onResumed 或 onStarted 中更有意义
            // currentActivityRef = WeakReference(activity) // 如果需要，可以取消注释
            extraCallbacks.forEach { it.onActivityCreated(activity, savedInstanceState) }
        }

        override fun onActivityStarted(activity: Activity) {
            startedActivityCount++
            // 只有当应用之前在后台，并且现在有 Activity 启动时，才认为是切换到前台
            if (!isAppInForeground && startedActivityCount > 0) {
                isAppInForeground = true
                foregroundListener?.invoke()
                // 检查是否从外部浏览器返回，如果是则调整轮询策略
                checkAndHandleExternalBrowserReturn()
            }
            currentActivityRef = WeakReference(activity) // 总是更新当前 Activity
            extraCallbacks.forEach { it.onActivityStarted(activity) }
        }

        override fun onActivityResumed(activity: Activity) {
            currentActivityRef = WeakReference(activity) // 确保 Resumed 时 currentActivityRef 是最新的
            // 当有 Activity resumed 时，应用肯定是在前台
            if (!isAppInForeground) { // 如果之前误判为后台，现在纠正
                isAppInForeground = true
                // 也可以考虑在这里再次调用 foregroundListener，但这取决于具体需求，
                // 通常在 onActivityStarted 中处理前后台切换事件更常见。
                // foregroundListener?.invoke()
            }
            extraCallbacks.forEach { it.onActivityResumed(activity) }
        }

        override fun onActivityPaused(activity: Activity) {
            extraCallbacks.forEach { it.onActivityPaused(activity) }
        }

        override fun onActivityStopped(activity: Activity) {
            startedActivityCount--
            // 只有当所有 Activity 都停止了，并且应用之前在前台时，才认为是切换到后台
            if (startedActivityCount <= 0 && isAppInForeground) {
                isAppInForeground = false
                backgroundListener?.invoke()
                // 应用进入后台时，不清空 currentActivityRef，除非 Activity 被销毁
                // 这样当应用快速返回时，仍能获取到之前的 Activity (如果它没被销毁)
            }
            extraCallbacks.forEach { it.onActivityStopped(activity) }
        }

        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
            extraCallbacks.forEach { it.onActivitySaveInstanceState(activity, outState) }
        }

        override fun onActivityDestroyed(activity: Activity) {
            val currentActivity = currentActivityRef?.get()
            // 仅当被销毁的 Activity 是当前持有的 Activity 时才清空引用
            if (currentActivity === activity) {
                currentActivityRef = null
            }
            extraCallbacks.forEach { it.onActivityDestroyed(activity) }
        }
    }

    fun init(application: Application) {
        application.registerActivityLifecycleCallbacks(activityLifecycleCallbacks)
    }

    fun registerExtraLifecycleCallbacks(callbacks: Application.ActivityLifecycleCallbacks) {
        extraCallbacks.add(callbacks)
    }

    fun unregisterExtraLifecycleCallbacks(callbacks: Application.ActivityLifecycleCallbacks) {
        extraCallbacks.remove(callbacks)
    }

    fun isAppInForeground(): Boolean = isAppInForeground
    fun isAppInBackground(): Boolean = !isAppInForeground
    fun getCurrentActivity(): Activity? = currentActivityRef?.get()
    fun getTopActivity(): Activity? = currentActivityRef?.get() // 逻辑与 getCurrentActivity 一致

    /**
     * 设置前台切换监听器
     * 当APP从后台切换到前台时会触发
     */
    fun setForegroundListener(listener: (() -> Unit)?) {
        foregroundListener = listener
    }

    /**
     * 设置后台切换监听器 (新增)
     * 当APP从前台切换到后台时会触发
     */
    fun setBackgroundListener(listener: (() -> Unit)?) {
        backgroundListener = listener
    }

    var lastState = false
    fun userModeSwitch() {
        if (lastState == isAppInForeground) {
            return
        }
        ThreadUtils.runOnIO { // 确保在 IO 线程执行网络请求
            try {
                // 使用 withContext 确保 Retrofit 调用在正确的调度器上
                // Retrofit 内部通常会处理线程切换，但显式指定更清晰
                val response = withContext(Dispatchers.IO) {
                    lastState = isAppInForeground
                    RetrofitUtils.dataRepository.userModeSwitch(
                        UserModeSwitchReq(
                            mode = if (isAppInBackground()) 1 else 0,
                        )
                    )
                }
                // 根据需要处理 response
            } catch (e: Exception) {
                // 处理网络请求或其他异常
                // Log.e(TAG, "userModeSwitch failed", e) // 例如记录错误
            }
        }
    }

    /**
     * 检查并处理从外部浏览器返回的情况
     *
     * 当应用从后台切换到前台时，发送支付返回事件
     * 由RechargeManager监听此事件并处理轮询策略调整
     */
    private fun checkAndHandleExternalBrowserReturn() {
        try {
            // 使用EventBus发送支付返回事件，避免直接依赖RechargeManager
            EventBus.post(CustomEvents.PaymentReturnEvent)
        } catch (e: Exception) {
            // 静默处理异常，避免影响应用正常运行
        }
    }
}
