package com.score.callmetest.entity

import android.net.Uri
import com.score.callmetest.util.AgodaCallEvent


/**
 * 自定义事件-- 不知道分类在哪的都放这里
 * <AUTHOR>
 * @date 2025/08/01
 * @constructor 创建[CustomEvents]
 */
sealed class CustomEvents {


    /**
     * 选择底部选项卡
     * <AUTHOR>
     * @date 2025/08/01
     * @constructor 创建[BottomTabSelected]
     * @param [index]
     */
    data class BottomTabSelected(val index: Int) : CustomEvents()

    /**
     * 审核模式下呼出
     * <AUTHOR>
     * @date 2025/08/04
     * @constructor 创建[ReviewCallEvent]
     * @param [userId] 用户id
     */
    data class ReviewCallEvent(val userId: String) : CustomEvents()

    /**
     * 支付流程返回事件
     * 用于处理从外部浏览器返回应用时的轮询策略调整
     * <AUTHOR>
     * @date 2025/08/14
     * @constructor 创建[PaymentReturnEvent]
     */
    object PaymentReturnEvent : CustomEvents()

}

/**
 * EventBus-Message相关
 */
sealed class MessageEvents {

    /**
     * 音频下载完成
     * @param [msgId] msg-id
     * @param [localUri] 本地uri
     */
    data class AudioDownloadOk(val msgId: String, val localUri: Uri) : MessageEvents()

}