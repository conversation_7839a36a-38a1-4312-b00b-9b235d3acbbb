package com.score.callmetest.ui.chat.adapter

import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemBroadcasterCardBinding
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.network.UserInfo
import com.score.callmetest.ui.chat.adapter.PhotoAlbumAdapter
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.ui.widget.decoration.SpaceHorItemDecoration
import timber.log.Timber

/**
 * 主播card ViewHolder
 * 用于在聊天列表中显示主播信息卡片
 */
class BroadcasterCardViewHolder(
    private val binding: ItemBroadcasterCardBinding,
    private val listeners: ChatAdapterListeners
) : RecyclerView.ViewHolder(binding.root) {

    private var mPhotoAlbumAdapter: PhotoAlbumAdapter? = null
    
    init {
        setupPhotoAlbumRecyclerView()
    }

    /**
     * 绑定主播信息数据
     * @param userInfo 主播用户信息
     * @param photoAlbumUrls 相册图片URL列表
     */
    fun bind(userInfo: UserInfo?, photoAlbumUrls: List<String>) {
        userInfo?.let { user ->
            // 设置年龄
            binding.age.text = user.age?.toString() ?: ""
            
            // 设置国家
            binding.country.text = user.country ?: ""
            
            // 设置语言
            binding.language.text = user.language ?: ""
            
            // 设置价格
            val unitPrice = user.unitPrice ?: 0
            binding.price.text = if (unitPrice > 0) "${unitPrice}/min" else ""
            
            // 设置背景颜色
            GlobalManager.setViewRoundBackground(
                binding.age,
                ContextCompat.getColor(binding.root.context, R.color.age_bg)
            )
            GlobalManager.setViewRoundBackground(
                binding.country,
                ContextCompat.getColor(binding.root.context, R.color.country_bg)
            )
            GlobalManager.setViewRoundBackground(
                binding.language,
                ContextCompat.getColor(binding.root.context, R.color.language_bg)
            )
        }

        // 设置相册图片
        mPhotoAlbumAdapter?.submitList(photoAlbumUrls)
    }

    /**
     * 初始化相册图片RecyclerView
     */
    private fun setupPhotoAlbumRecyclerView() {
        mPhotoAlbumAdapter = PhotoAlbumAdapter()
        binding.rvPhotoAlbum.layoutManager = LinearLayoutManager(
            binding.root.context, 
            LinearLayoutManager.HORIZONTAL, 
            false
        )
        binding.rvPhotoAlbum.adapter = mPhotoAlbumAdapter
        
        // 添加12dp空白分割线
        val spacingInPixels = DisplayUtils.dp2px(12f)
        binding.rvPhotoAlbum.addItemDecoration(SpaceHorItemDecoration(spacingInPixels))

        // 设置点击事件
        mPhotoAlbumAdapter?.setOnPhotoClickListener { photoUrl, position ->
            Timber.tag("dsc--").d("相册图片点击：$photoUrl, 位置：$position")
            // 通过listeners传递点击事件给Activity处理
            listeners.mOnPhotoClickListener?.invoke(photoUrl, position, mPhotoAlbumAdapter?.currentList ?: emptyList())
        }
    }
}
