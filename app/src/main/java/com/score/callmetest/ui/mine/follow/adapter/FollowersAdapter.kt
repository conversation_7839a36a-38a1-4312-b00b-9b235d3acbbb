package com.score.callmetest.ui.mine.follow.adapter

import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication.Companion.context
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemFragmentFollowersBinding
import com.score.callmetest.databinding.ItemListBottomBinding
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.network.FollowModel
import com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity

import com.score.callmetest.util.CountryUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.click

class FollowersAdapter(
    private val onFollowClick: (FollowModel, Int) -> Unit,
    private val onStateChange: ((String, Boolean) -> Unit)? = null // 状态变化回调
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    companion object {
        private const val VIEW_TYPE_FOLLOWER = 0
        private const val VIEW_TYPE_BOTTOM = 1
    }
    private val followers = mutableListOf<FollowModel>()
    private var showBottom: Boolean = true

    // 本地关注状态管理，key为userId，value为是否关注
    private val localFollowStates = mutableMapOf<String, Boolean>()

    fun setShowBottom(show: Boolean) {
        if (this.showBottom != show) {
            this.showBottom = show
            notifyDataSetChanged()
        }
    }

    fun setData(newFollowers: List<FollowModel>) {
        followers.clear()
        followers.addAll(newFollowers)

        // 初始化本地关注状态，使用原始数据
        localFollowStates.clear()
        newFollowers.forEach { followModel ->
            localFollowStates[followModel.userId] = followModel.isFollows ?: false
        }

        notifyDataSetChanged()
    }

    override fun getItemCount(): Int = followers.size + if (showBottom) 1 else 0

    override fun getItemViewType(position: Int): Int {
        return if (showBottom && position == followers.size) VIEW_TYPE_BOTTOM else VIEW_TYPE_FOLLOWER
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_FOLLOWER -> {
                val binding = ItemFragmentFollowersBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                ViewHolder(binding)
            }
            VIEW_TYPE_BOTTOM -> {
                val binding = ItemListBottomBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                BottomViewHolder(binding)
            }
            // todo 异常类型主动报错，后面可以删除
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is ViewHolder && position < followers.size) {
            val followModel = followers[position]
            holder.bind(followModel, position)
        } else if (holder is BottomViewHolder) {
            holder.bind("Bottom")
        }
    }

    // 跳转到主播详情页面
    private fun ToBroadcasterDetailActivity(context: Context,position: Int) {
        if (position != RecyclerView.NO_POSITION && position < followers.size) {
            val followModel = followers[position]
            val broadcasterModel = followModelToBroadcasterModel(followModel)
            val intent = Intent(context, BroadcasterDetailActivity::class.java)
            intent.putExtra("broadcaster_model", broadcasterModel)
            context.startActivity(intent)
        }
    }

    /**
     * 更新关注按钮UI状态
     */
    private fun updateFollowButtonUI(
        button: TextView,
        isFollowing: Boolean,
        cornerRadius: Float,
    ) {
        if (isFollowing) {
            button.text = "Following"
            val fillColor = ContextCompat.getColor(context, R.color.following_button_fill)
            button.background = DrawableUtils.createRoundRectDrawableDp(fillColor, cornerRadius)
        } else {
            button.text = "+ Follow"
            button.setTextColor(ContextCompat.getColor(context, R.color.follow_text_color))
            button.background = DrawableUtils.createRoundRectDrawableDp(
                ContextCompat.getColor(context, R.color.follow_button_fill),
                cornerRadius,
            )
        }
    }


    private fun followModelToBroadcasterModel(followModel: FollowModel): BroadcasterModel {
        return BroadcasterModel(
            userId = followModel.userId,
            nickname = followModel.nickname,
            avatar = followModel.avatar,
            gender = followModel.gender,
            age = followModel.age,
            country = followModel.country,
            status = followModel.onlineStatus ?: CallStatus.OFFLINE,
            callCoins = followModel.unitPrice,
            unit = "min", // 通话单位，默认"min"
            isFriend = followModel.mutualFlow,
            about = followModel.about,
            grade = followModel.level,
            analysisLanguage = followModel.language,
            isSignBroadcaster = followModel.isSignBroadcaster,
            showRoomVersion = followModel.showRoomVersion,
            broadcasterType = followModel.userType,
            avatarThumbUrl = followModel.avatarThumbUrl,
            isVip = followModel.isVip ?: false,
        )
    }

    inner class ViewHolder(private val binding: ItemFragmentFollowersBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(followModel: FollowModel, position: Int) {
            // 昵称
            binding.tvUsername.text = followModel.nickname ?: ""

            // 国家/地区
            binding.tvRegion.text = followModel.country ?: ""
            binding.ivFlag.setImageResource(CountryUtils.getIconByEnName(followModel.country))

            // 头像
            val avatarUrl = followModel.avatarUrl ?: followModel.avatarThumbUrl ?: ""
            if (avatarUrl.isNotEmpty()) {
                Glide.with(binding.ivAvatar.context)
                    .load(avatarUrl)
                    .placeholder(R.drawable.placeholder)
                    .error(R.drawable.placeholder)
                    .into(binding.ivAvatar)
            } else {
                binding.ivAvatar.setImageResource(R.drawable.placeholder)
            }

            // 关注按钮状态 - 使用本地状态管理
            val cornerRadius = 14f
            val isFollowing = localFollowStates[followModel.userId] ?: false

            updateFollowButtonUI(binding.tvAddFollow, isFollowing, cornerRadius)

            // 头像点击事件
            binding.ivAvatar.click {
                ToBroadcasterDetailActivity(itemView.context, adapterPosition)
            }

            // item点击事件
            itemView.click {
                ToBroadcasterDetailActivity(itemView.context, adapterPosition)
            }

            // 关注按钮点击事件
            binding.tvAddFollow.click {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION && position < followers.size) {
                    val followModel = followers[position]

                    // 立即更新本地状态和UI
                    val currentState = localFollowStates[followModel.userId] ?: false
                    val newState = !currentState
                    localFollowStates[followModel.userId] = newState

                    // 立即更新UI
                    val cornerRadius = 14f
                    updateFollowButtonUI(binding.tvAddFollow, newState, cornerRadius)

                    // 通知状态变化
                    onStateChange?.invoke(followModel.userId, newState)

                    // 调用回调处理网络请求
                    onFollowClick(followModel, position)
                }
            }
        }
    }

    inner class BottomViewHolder(private val binding: ItemListBottomBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(text: String) {
            binding.tvBottomText.text = text
        }
    }
} 