package com.score.callmetest.ui.widget

import android.content.Context
import android.graphics.Typeface
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import com.opensource.svgaplayer.SVGAImageView
import com.score.callmetest.R
import com.score.callmetest.manager.FollowManager
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.EventBus
import kotlinx.coroutines.CoroutineScope
import org.chromium.base.ThreadUtils.runOnUiThread

class FollowButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private lateinit var followBgSvga: SVGAImageView
    private val followIcon: ImageView
    private val followActionSvga: SVGAImageView
    private val followText: TextView
    private var isFriend: Boolean = false
    private var followHighlightTaskActive = false
    private val followHighlightHandler = Handler(Looper.getMainLooper())
    private var followHighlightRunnable = object : Runnable {
        override fun run() {
            if (followHighlightTaskActive) {
                CustomUtils.playSvgaOnce(followBgSvga, "follow_highlight.svga")
                followHighlightHandler.postDelayed(this, 10000)
            }
        }
    }

    init {
        LayoutInflater.from(context).inflate(R.layout.view_follow_button, this, true)
        followBgSvga = findViewById(R.id.follow_bg_svga)
        followIcon = findViewById(R.id.follow_icon)
        followActionSvga = findViewById(R.id.follow_action_svga)
        followText = findViewById(R.id.follow_text)


        followText.setTypeface(Typeface.create("sans-serif", Typeface.BOLD));
    }

    fun setIsFriend(friend: Boolean) {
        isFriend = friend
        if (friend) {
            // 已关注，隐藏按钮，停止动画
            visibility = View.GONE
            followHighlightTaskActive = false
            followActionSvga.stopAnimation()
            stopFollowHighlightTask()
        } else {
            // 未关注，显示按钮，重置状态
            visibility = View.VISIBLE
            isEnabled = true
            followIcon.visibility = View.VISIBLE
            followText.visibility = View.VISIBLE
            followActionSvga.stopAnimation()
            followActionSvga.visibility = View.GONE
            followHighlightTaskActive = true
            startFollowHighlightTask()
        }
    }

    fun playFollowAnim(onEnd: (() -> Unit)? = null) {
        isEnabled = false
        followIcon.visibility = View.GONE

        // 先停止之前的动画，清理状态
        followActionSvga.stopAnimation()
        followActionSvga.visibility = View.VISIBLE

        CustomUtils.playSvga(followActionSvga, "do_follow.svga", loops = 1, onFinished = {
            onEnd?.invoke()
        })
    }

    fun startFollowHighlightTask() {
        if (!followHighlightTaskActive) return
        followHighlightHandler.removeCallbacks(followHighlightRunnable)
        followHighlightTaskActive = true
        followHighlightHandler.post(followHighlightRunnable)
    }

    fun stopFollowHighlightTask() {
        followHighlightTaskActive = false
        followHighlightHandler.removeCallbacks(followHighlightRunnable)
        followBgSvga.stopAnimation()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        followHighlightTaskActive = false
        followHighlightHandler.removeCallbacks(followHighlightRunnable)
        followActionSvga.stopAnimation()
        followBgSvga.stopAnimation()
    }

    fun addFriend(scope: CoroutineScope, userId: String, callback: (Boolean) -> Unit) {
        isEnabled = false

        FollowManager.followUser(userId, object : FollowManager.FollowActionCallback {
            override fun onSuccess() {
                runOnUiThread {
                    playFollowAnim {
                        // 动画播放完成后再发送事件和隐藏按钮
                        EventBus.post(FollowEvent(userId, true))
                        setIsFriend(true)
                    }
                    callback.invoke(true)
                }
            }

            override fun onError(errorMsg: String) {
                runOnUiThread {
                    EventBus.post(FollowEvent(userId, false))
                    setIsFriend(false)
                    callback.invoke(false)
                }
            }
        })
    }
    // 取关 - 通过FollowManager
    fun unfollow(scope: CoroutineScope, userId: String, callback: (Boolean) -> Unit) {
        isEnabled = false

        FollowManager.unfollowUser(userId, object : FollowManager.FollowActionCallback {
            override fun onSuccess() {
                runOnUiThread {
                    EventBus.post(FollowEvent(userId, false))
                    // 取关成功后直接显示按钮，不需要播放动画
                    setIsFriend(false)
                    callback.invoke(true)
                }
            }

            override fun onError(errorMsg: String) {
                runOnUiThread {
                    EventBus.post(FollowEvent(userId, true))
                    setIsFriend(true)
                    callback.invoke(false)
                }
            }
        })
    }
}

data class FollowEvent(
    val userId: String,
    val isFriend: Boolean
)

//拉黑事件
data class BlockEvent(
    val userId: String,
    val isBlocked: Boolean
)