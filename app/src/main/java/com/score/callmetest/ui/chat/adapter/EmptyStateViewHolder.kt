package com.score.callmetest.ui.chat.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R

/**
 * 空状态ViewHolder
 * 显示无消息时的提示界面
 */
class EmptyStateViewHolder(parent: ViewGroup) : RecyclerView.ViewHolder(
    LayoutInflater.from(parent.context).inflate(R.layout.layout_empty_rv_bg, parent, false)
) {
    // 空状态不需要绑定数据，布局文件已经包含了所有必要的内容
}
