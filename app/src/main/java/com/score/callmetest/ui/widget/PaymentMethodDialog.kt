package com.score.callmetest.ui.widget

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.score.callmetest.databinding.DialogPaymentMethodSelectBinding
import com.score.callmetest.entity.MessageListEntity
import com.score.callmetest.entity.isSysService
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.GoodsManager
import com.score.callmetest.manager.PaymentMethodManager
import com.score.callmetest.manager.RechargeManager
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.network.PayChannelItem
import com.score.callmetest.ui.widget.adapter.PaymentMethodAdapter
import com.score.callmetest.ui.widget.decoration.SpaceVerticalItemDecoration
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.click
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

class PaymentMethodDialog(
    private val goodsInfo: GoodsInfo,
    private val onPaymentMethodSelected: (String) -> Unit
) : BottomSheetDialogFragment() {

    private var _binding: DialogPaymentMethodSelectBinding? = null
    private val binding get() = _binding!!
    private lateinit var adapter: PaymentMethodAdapter
    private val mPaymentMethodList = mutableListOf<PaymentMethodItem>()
    private var mSelectedPosition = -1

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        _binding = DialogPaymentMethodSelectBinding.inflate(inflater, container, false)
        binding.bottomShadow.background = DrawableUtils.createGradientDrawable(
            colors = intArrayOf("#00FFFFFF".toColorInt(), "#FFFFFF".toColorInt()),
            orientation = GradientDrawable.Orientation.TOP_BOTTOM,
        )
        setupRecyclerView()
        setupButtons()
        setupProductInfo()
        loadPaymentMethods()
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun setupRecyclerView() {
        binding.rvPaymentMethods.layoutManager = LinearLayoutManager(requireContext())
        binding.rvPaymentMethods.addItemDecoration(SpaceVerticalItemDecoration(DisplayUtils.dp2px(10f)))
        adapter = PaymentMethodAdapter(mPaymentMethodList, goodsInfo) { position,payChannel ->
           /* if (payChannel == PaymentMethodManager.PAY_CHANNEL_GP) {
                // 可在此处直接处理 GP 支付逻辑，或通过回调让外部处理
                onPaymentMethodSelected("GP")
            } else {
                onPaymentMethodSelected(payChannel)
            }*/
            if(mSelectedPosition != -1) {
                mPaymentMethodList[mSelectedPosition].isSelected = false
                adapter.notifyItemChanged(mSelectedPosition)
            }
            mPaymentMethodList[position].isSelected = true
            mSelectedPosition = position
            adapter.notifyItemChanged(position)
        }
        binding.rvPaymentMethods.adapter = adapter
    }

    private fun setupButtons() {
        binding.btnPay.click {
            dismiss()
            if(mSelectedPosition == -1 || mSelectedPosition >= mPaymentMethodList.size) return@click
            onPaymentMethodSelected(mPaymentMethodList[mSelectedPosition].payChannel)
        }
    }

    private fun setupProductInfo() {
        // 显示商品信息
        val coinAmount = goodsInfo.exchangeCoin ?: 0
        val extraCoin = goodsInfo.extraCoin ?: 0

        binding.tvCoinBalance.text = "$coinAmount"
        if(extraCoin > 0) binding.tvCoinMore.text = "+$extraCoin"

        val (priceSymbol, price) = GoodsManager.getLocaleGoodsPrice(goodsInfo)
        binding.tvPrice.text = "$priceSymbol$price"
    }

    private fun loadPaymentMethods() {
        // 先获取支付渠道列表，然后计算折扣
        CoroutineScope(Dispatchers.Main).launch {
            val payChannelList = PaymentMethodManager.ensurePayChannelListLoaded()
            val defaultMethod = PaymentMethodManager.getDefaultPaymentMethod()
            val lastUsedMethod = PaymentMethodManager.getLastUsedPaymentMethod()
            val paymentMethodList = mutableListOf<PaymentMethodItem>()
            payChannelList?.forEachIndexed { index,item ->
                val discount = calculateChannelDiscount(item)
                val isSelected = item.payChannel == defaultMethod
                paymentMethodList.add(
                    PaymentMethodItem(
                        payChannel = item.payChannel ?: "",
                        displayName = item.title ?: item.payChannel ?: "",
                        iconRes = -1,
                        iconUrl = item.iconUrl ?: "",
                        isRecommend = item.itemType == 1,
                        isLastUsed = lastUsedMethod == item.payChannel,
                        discount = discount,
                        isSelected = isSelected
                    )
                )
            }
            mPaymentMethodList.addAll(
                paymentMethodList.sortedWith(
                    compareByDescending<PaymentMethodItem> {
                        // 1. 上一次优先
                        it.isLastUsed
                    }
                        .thenByDescending { it.isRecommend }                       // 2. 推荐其次
                )
            )
            mSelectedPosition = mPaymentMethodList.indexOfFirst{it.isSelected}
            adapter.notifyDataSetChanged()
        }
    }

    /**
     * 根据商品类型和支付渠道计算折扣
     */
    private fun calculateChannelDiscount(payChannel: PayChannelItem?): Int {
        if (payChannel == null) return 0
        return when (goodsInfo.type) {
            "0" -> payChannel.presentCoinRatio ?: 0  // 普通商品
            "1" -> payChannel.promotionPresentCoinRatio ?: 0  // 促销商品
            "2" -> {  // 活动商品：取thirdpartyCoinPercent和promotionPresentCoinRatio的最大值
                val thirdpartyCoinPercent = goodsInfo.thirdpartyCoinPercent ?: 0
                val promotionPresentCoinRatio = payChannel.promotionPresentCoinRatio ?: 0
                maxOf(thirdpartyCoinPercent, promotionPresentCoinRatio)
            }
            "3" -> payChannel.presentCoinRatio ?: 0  // 订阅商品
            else -> payChannel.presentCoinRatio ?: 0  // 默认使用普通商品的比例
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?) = super.onCreateDialog(savedInstanceState).apply {
        setOnShowListener {
            val bottomSheet = (this as? BottomSheetDialog)
                ?.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.setBackgroundColor(Color.TRANSPARENT)
        }
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
    }
}

data class PaymentMethodItem(
    val payChannel: String,
    val displayName: String,
    val iconRes: Int,
    var iconUrl: String? = null,
    var isRecommend: Boolean = false,
    var isLastUsed: Boolean = false,
    val discount: Int,
    var isSelected: Boolean
)