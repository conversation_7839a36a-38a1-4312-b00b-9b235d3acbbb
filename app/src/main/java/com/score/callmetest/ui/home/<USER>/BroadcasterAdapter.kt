package com.score.callmetest.ui.home.adapter

import android.Manifest
import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.text.style.ImageSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemBannerHeaderBinding
import com.score.callmetest.databinding.ItemBroadcasterBinding
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.manager.BannerManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.SocketManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.BannerInfoResponse
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity
import com.score.callmetest.ui.login.LoginActivity
import com.score.callmetest.ui.videocall.VideoCallActivity
import com.score.callmetest.ui.web.WebViewActivity
import com.score.callmetest.ui.widget.Helper.PagerHelper
import com.score.callmetest.ui.widget.InsufficientBalanceDialog
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.CountryUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.LoadingUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import com.score.callmetest.util.logAsTag
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import timber.log.Timber

class BroadcasterAdapter :
    ListAdapter<BroadcasterAdapter.ListItem, RecyclerView.ViewHolder>(ListItemDiffCallback()) {

    // 定义视图类型常量
    companion object {
        const val VIEW_TYPE_BANNER = 0
        const val VIEW_TYPE_BROADCASTER = 1
    }

    // 密封类，用于表示列表中的不同类型项
    sealed class ListItem {
        data class BannerItem(val bannerList: List<BannerInfoResponse>) :
            ListItem() // Banner 通常不需要特定数据，如果需要可以添加

        data class BroadcasterItem(val broadcaster: BroadcasterModel) : ListItem()

        // 为 DiffUtil 提供一个唯一标识符，特别是对于数据类
        val id: String
            get() = when (this) {
                is BannerItem -> "banner" // Banner 可以有一个固定的ID
                is BroadcasterItem -> broadcaster.userId // 主播项使用其唯一ID
            }
    }

    class ListItemDiffCallback : DiffUtil.ItemCallback<ListItem>() {
        override fun areItemsTheSame(oldItem: ListItem, newItem: ListItem): Boolean {
            // 比较唯一ID
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: ListItem, newItem: ListItem): Boolean {
            // 如果是 BannerItem，且只有一个实例，它们总是相同的
            if (oldItem is ListItem.BannerItem && newItem is ListItem.BannerItem) {
                return true
            }
            // 对于 BroadcasterItem，比较其内容
            if (oldItem is ListItem.BroadcasterItem && newItem is ListItem.BroadcasterItem) {
                return oldItem.broadcaster == newItem.broadcaster
            }
            // 其他情况或类型不匹配
            return false
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (val item = getItem(position)) {
            is ListItem.BannerItem -> VIEW_TYPE_BANNER
            is ListItem.BroadcasterItem -> VIEW_TYPE_BROADCASTER
            else -> {
                throw IllegalStateException("Item at position $position is null")
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            VIEW_TYPE_BANNER -> {
                val binding = ItemBannerHeaderBinding.inflate(inflater, parent, false)
                binding.bannerViewPager.doOnPreDraw {
                    binding.bannerViewPager.layoutParams.apply {
                        height = (binding.bannerViewPager.width * 0.304f).toInt()
                    }
                    binding.bannerViewPager.requestLayout()
                }
                BannerViewHolder(binding)
            }

            VIEW_TYPE_BROADCASTER -> {
                val binding = ItemBroadcasterBinding.inflate(inflater, parent, false)
                BroadcasterViewHolder(binding)
            }

            else -> throw IllegalArgumentException("Invalid view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (val item = getItem(position)) {
            is ListItem.BannerItem -> {
                (holder as BannerViewHolder).bind(item.bannerList) // Banner ViewHolder 的绑定逻辑
            }

            is ListItem.BroadcasterItem -> {
                (holder as BroadcasterViewHolder).bind(this, item.broadcaster, position)
            }

            null -> {
                // 通常不应该发生，但作为安全措施可以记录错误
            }
        }
    }

    // Banner ViewHolder
    class BannerViewHolder(private val binding: ItemBannerHeaderBinding) :
        RecyclerView.ViewHolder(binding.root) {
        private var lastHighlightDotPosition = -1

        @SuppressLint("ClickableViewAccessibility")
        fun bind(bannerList: List<BannerInfoResponse>) {
            PagerHelper.setupBannerPager(
                viewPager = binding.bannerViewPager,
                bannerList = bannerList,
                onItemClick = { realIndex ->
                    if (realIndex < bannerList.size) {
                        BannerManager.handleBannerClick(
                            context = binding.bannerViewPager.context,
                            bannerInfoResponse = bannerList[realIndex]
                        )
                    }
                },
                onPageSelected = { realIndex ->
                    refreshDot(bannerList, realIndex)
                }
            )
            refreshDot(bannerList, 0)
        }

        fun refreshDot(bannerList: List<BannerInfoResponse>, realIndex: Int) {
            if (lastHighlightDotPosition == realIndex) {
                return
            }
            lastHighlightDotPosition = realIndex
            binding.dotLayout.removeAllViews()
            val dotCount = bannerList.size
            for (i in 0..dotCount - 1) {
                val dotSize = DisplayUtils.dp2px(5f)
                val dot = ImageView(binding.root.context)
                val params = ViewGroup.MarginLayoutParams(
                    dotSize,
                    dotSize
                ).apply {
                    if (i > 0) {
                        marginStart = DisplayUtils.dp2px(3f)
                    }
                }
                dot.layoutParams = params
                dot.setImageDrawable(
                    DrawableUtils.createRoundRectDrawable(
                        radius = dotSize / 2f,
                        color = if (i == lastHighlightDotPosition) Color.WHITE else "#88FFFFFF".toColorInt()
                    )
                )
                binding.dotLayout.addView(dot)
            }
        }
    }

    // Broadcaster ViewHolder (你现有的 ViewHolder)
    class BroadcasterViewHolder(private val binding: ItemBroadcasterBinding) :
        RecyclerView.ViewHolder(binding.root) {
        // 用于管理动画循环的 Runnable
        private var animationRunnable: Runnable? = null

        fun bind(adapter: BroadcasterAdapter, broadcaster: BroadcasterModel, itemPosition: Int) {
            binding.apply {
                bgMask.background = DrawableUtils.createGradientDrawable(
                    colors = intArrayOf(Color.TRANSPARENT, "#881B1C20".toColorInt()),
                    orientation = GradientDrawable.Orientation.TOP_BOTTOM
                )
                // 加载头像 - 优先使用avatarThumbUrl，如果没有则使用avatar
                Glide.with(avatar)
                    .load(broadcaster.avatar ?: broadcaster.avatarThumbUrl)
                    .placeholder(R.drawable.placeholder)
                    .error(R.drawable.placeholder)
                    .into(avatar)

                // 设置文本信息
                nickname.text = broadcaster.nickname
                GlobalManager.setTextTypefaceSansSerif(nickname)

                // 设置国家图标 - 先将国家名称转换为ISO代码，再查找图标
                val countryName = broadcaster.country ?: "USA"
                val countryIconRes = if (countryName == "USA" || countryName == "United States") {
                    CountryUtils.getIconByIsoFromLibrary("US")
                } else {
                    CountryUtils.getIconByEnNameFromLibrary(countryName)
                }
                imageRegion.setImageResource(countryIconRes)

                // 设置价格文本 - 显示带金币图标的价格
                val price = broadcaster.callCoins ?: 0
                tvPrice.text = CustomUtils.createCoinSpannableText(
                    context = itemView.context,
                    text = "${price} icon/min",
                    coinSizeDp = 9f,
                    alignment = ImageSpan.ALIGN_BASELINE,
                    spacesBefore = 0,
                    spacesAfter = 1
                )

                // item点击事件
                itemView.click {
                    val context = itemView.context
                    val intent = Intent(context, BroadcasterDetailActivity::class.java)
                    intent.putExtra("broadcaster_model", broadcaster)
                    context.startActivity(intent)
                }

                GlobalManager.setViewRoundBackground(statusLayout, "#33000000".toColorInt())
                GlobalManager.setViewRoundBackground(
                    statusIndicator, GlobalManager.getStatusColor(broadcaster.status)
                )
                statusText.text = broadcaster.status

                var status =
                    UserInfoManager.getCachedStatus(broadcaster.userId) ?: broadcaster.status

                if (StrategyManager.isReviewPkg()) {
                    if (broadcaster.isAnswer == true && !StrategyManager.reviewPkgUsers.contains(
                            broadcaster.userId
                        )
                    ) {
                        status = CallStatus.ONLINE
                    } else {
                        status = GlobalManager.getReviewOtherStatus(broadcaster.userId)
                    }
                }


                if (status == CallStatus.ONLINE ||
                    status == CallStatus.AVAILABLE
                ) {
                    btnVideoSvga.visibility = View.VISIBLE
                    btnVideoGray.visibility = View.GONE
                    btnVideoSvga.isClickable = true

                    // 清理之前的动画任务
                    animationRunnable?.let { btnVideoSvga.removeCallbacks(it) }

                    // 关键修复：只有当动画没有在播放时才重新播放
                    if (!btnVideoSvga.isAnimating) {
                        startPeriodicAnimation()
                    }
                    btnVideoSvga.click {
                        val activity = itemView.context as? androidx.appcompat.app.AppCompatActivity
                        if (activity == null) {
//                        ToastUtils.showToast("页面异常，无法发起通话")
                            return@click
                        }
                        // 非审核模式下先判断金币是否足够
                        if (!StrategyManager.isReviewPkg()) {
                            val availableCoins = UserInfoManager.myUserInfo?.availableCoins ?: 0
                            val unitPrice = broadcaster.callCoins ?: 0
                            if (availableCoins < unitPrice) {

                                // 弹出金币充值弹窗（新版：自动拉取商品数据,显示主播每分钟收费）
                                val dialog = InsufficientBalanceDialog.newInstance(unitPrice, RechargeSource.ANCHOR_WALL.value())
                                dialog.show(activity.supportFragmentManager, "insufficient_balance")

                                return@click
                            }
                        }

                        // 检查网络连接
                        if (!SocketManager.instance.isConnected()) {
//                        ToastUtils.showToast("Socket service is offline")
                            Timber.d("Socket service is offline")
                            ToastUtils.showToast(CallmeApplication.context.getString(R.string.net_error_and_try_again))
                            return@click
                        }
                        // 3. 金币和网络检查通过后，再检查权限
                        AppPermissionManager.checkAndRequestCameraMicrophonePermission(
                            activity,
                            onGranted = {
                                // 权限授权成功，再次确认在线状态
                                LoadingUtils.showLoading(activity)
                                UserInfoManager.loadOnlineStatus(
                                    scope = CoroutineScope(Dispatchers.IO),
                                    userId = broadcaster.userId,
                                    callback = { status, error ->
                                        ThreadUtils.runOnMain {
                                            LoadingUtils.dismissLoading()
                                            if (error == null && status != null) {
                                                status.toString()
                                                    .logAsTag(this.javaClass.name + "status: ")
                                                broadcaster.status = status
                                                adapter.notifyItemChanged(itemPosition)
                                                when (status) {
                                                    CallStatus.ONLINE, CallStatus.AVAILABLE -> {
                                                        VideoCallActivity.startOutgoing(
                                                            context = activity,
                                                            userId = broadcaster.userId,
                                                            avatarUrl = broadcaster.avatarThumbUrl.toString(),
                                                            nickname = broadcaster.nickname.toString(),
                                                            age = broadcaster.age.toString(),
                                                            country = broadcaster.country.toString(),
                                                            unitPrice = broadcaster.unit.toString()
                                                        )
                                                    }

                                                    else -> {
                                                        ToastUtils.showToast(
                                                            CallmeApplication.context.getString(
                                                                R.string.user_not_available
                                                            )
                                                        )
                                                    }
                                                }
                                            }
                                        }
                                    }
                                )
                            },
                            onDenied = {
                                ToastUtils.showToast(CallmeApplication.context.getString(R.string.camera_microphone_permission_required))
                                val s = AppPermissionManager.shouldShowRequestPermissionRationale(
                                    activity,
                                    Manifest.permission.RECORD_AUDIO
                                )
                                if (s) {
                                    Timber.d("ask") // 可以再次询问权限
                                } else {
                                    Timber.d("onDenied") // 权限被永久拒绝，跳转设置页面
                                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.camera_microphone_permission_check_hint))
                                    AppPermissionManager.openAppSettings(activity)
                                }
                            }
                        )
                    }

                } else {
                    // 清理动画任务和停止SVGA动画
                    animationRunnable?.let { btnVideoSvga.removeCallbacks(it) }
                    btnVideoSvga.stopAnimation()
                    btnVideoSvga.visibility = View.GONE
                    btnVideoGray.visibility = View.VISIBLE
                    btnVideoGray.isClickable = true
                    btnVideoGray.click {
                        val activity = itemView.context as? androidx.appcompat.app.AppCompatActivity
                        if (activity == null) {
                            return@click
                        }

                        // 非审核模式下先判断金币是否足够
                        if (!StrategyManager.isReviewPkg()) {
                            val availableCoins = UserInfoManager.myUserInfo?.availableCoins ?: 0
                            val unitPrice = broadcaster.callCoins ?: 0
                            if (availableCoins < unitPrice) {
                                // 弹出金币充值弹窗（新版：自动拉取商品数据,显示主播每分钟收费）
                                val dialog = InsufficientBalanceDialog.newInstance(unitPrice,RechargeSource.ANCHOR_WALL.value())
                                dialog.show(activity.supportFragmentManager, "insufficient_balance")
                                return@click
                            }

                            // 金币充足但主播状态不可用时显示toast
                            val statusText = CallStatus.getDisplayText(broadcaster.status)
                            val message =
                                activity.getString(R.string.user_status_not_available, statusText)
                            ToastUtils.showToast(message)
                        }
                    }
                }
            }
        }

        /**
         * 开始周期性动画播放
         * 性能优化：播放3次后暂停1秒，然后重复
         */
        private fun startPeriodicAnimation() {
            CustomUtils.playSvga(
                binding.btnVideoSvga,
                "btn_broadcaster.svga",
                loops = 3, // 播放3次
                onFinished = {
                    // 动画完成后，间隔1秒再次播放
                    animationRunnable = Runnable {
                        if (binding.btnVideoSvga.isVisible) {
                            startPeriodicAnimation()
                        }
                    }
                    binding.btnVideoSvga.postDelayed(animationRunnable, 1000) // 1秒间隔
                }
            )
        }
    }

    /**
     * 更新主播的状态。
     * @param statusList 一个包含 (userId, newStatus) 对的列表。
     */
    fun updateBroadcasterStatus(statusList: List<BroadcasterModel>) { // 假设 status 是 String
        val newList = currentList.map { listItem ->
            if (listItem is ListItem.BroadcasterItem) {
                val statusUpdate = statusList.find { it.userId == listItem.broadcaster.userId }
                if (statusUpdate != null) {
                    // 创建新的 BroadcasterModel 实例以触发 DiffUtil 更新
                    listItem.copy(broadcaster = listItem.broadcaster.copy(status = statusUpdate.status))
                } else {
                    listItem
                }
            } else {
                listItem // BannerItem 或其他类型的项保持不变
            }
        }
        submitList(newList)
    }
}