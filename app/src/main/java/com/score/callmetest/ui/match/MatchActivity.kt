package com.score.callmetest.ui.match

import android.os.Handler
import android.os.Looper
import android.view.KeyEvent
import androidx.activity.OnBackPressedCallback
import androidx.lifecycle.lifecycleScope
import com.score.callmetest.CallmeApplication
import com.score.callmetest.HangupScene
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityMatchBinding
import com.score.callmetest.manager.AppLifecycleManager
import com.score.callmetest.manager.AudioPlayManager
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.FlashChatManager
import com.score.callmetest.manager.HangUpReason
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.manager.VideoCallManager
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.videocall.VideoCallActivity
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Random

/**
 * 匹配页面
 */
class MatchActivity : BaseActivity<ActivityMatchBinding, MatchViewModel>() {

    private var channelName: String? = null
    private var oppositeUserId: String? = null
    override fun getViewBinding(): ActivityMatchBinding {
        return ActivityMatchBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = MatchViewModel::class.java

    override fun initView() {
        startRingtone()
    }

    override fun initListener() {
        // 拦截系统返回（含手势返回与预测性返回）
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // 消费返回事件，不执行退出
            }
        })

        CustomUtils.playSvga(binding.matchIcon, "matching.svga")
        showText()
        binding.close.click {
            FlashChatManager.matchCancel(
                scope = lifecycleScope
            ) {
                VideoCallManager.hangUp(
                    scope = lifecycleScope,
                    channelName = channelName.toString(),
                    oppositeUserId = oppositeUserId ?: UserInfoManager.myUserInfo?.userId.toString(),
                    hangUpReason = HangUpReason.NORMAL,
                    remark = HangupScene.FLASH_CHAT_HANGUP,
                )
                finish()
            }
        }

        FlashChatManager.flashChat(
            scope = lifecycleScope,
            callback = { flashChatResponse ->
                if (flashChatResponse != null &&
                    !flashChatResponse.channelName.isNullOrEmpty() &&
                    !flashChatResponse.rtcToken.isNullOrEmpty()
                ) {
                    startCallTimeouts()
                    channelName = flashChatResponse.channelName
                    oppositeUserId = flashChatResponse.fromUserId

                    // toUserId != "0"说明匹配到了主播，随机延迟后加入频道
                    if (flashChatResponse.toUserId != "0") {
                        matchingTextJob?.cancel()
                        binding.matchText.text = "Connecting..."
                        val ranDelay = Random().nextInt(5000).toLong()
                        ThreadUtils.runOnMainDelayed(ranDelay) {
                            VideoCallActivity.startOngoing(
                                context = this@MatchActivity,
                                channelName = flashChatResponse.channelName.toString(),
                                fromUserId = flashChatResponse.toUserId.toString(),
                                toUserId = flashChatResponse.fromUserId.toString(),
                                freeCallDuration = flashChatResponse.callFreeSeconds,
                                rtcToken = flashChatResponse.rtcToken,
                            )
                            finish()
                        }
                        return@flashChat
                    }

                    // 收到加入频道成功消息，则监听挂断或接听事件
                    // 等待长链接收 接听 事件（使用双通道去重）
                    DualChannelEventManager.observeOnHangUp(this) { event ->
                        if (channelName == event.channelName) {
                            VideoCallManager.hangUp(
                                scope = lifecycleScope,
                                channelName = event.channelName.toString(),
                                oppositeUserId = event.toUserId.toString(),
                                hangUpReason = HangUpReason.CL_REMOTE_USER_LEFT,
                                remark = null,
                            )
                            finish()
                        }
                    }

                    DualChannelEventManager.observeOnPickUp(this) { pickUpMsg ->
                        // 这里是对方接听后回调。
                        // 断点发现pickUpMsg.fromUserId是对方ID，pickUpMsg.toUserId是自己ID
                        // 所以这里要更换位置
                        matchingTextJob?.cancel()
                        binding.matchText.text = "Connecting..."
                        VideoCallActivity.startOngoing(
                            context = this@MatchActivity,
                            channelName = pickUpMsg.channelName.toString(),
                            fromUserId = pickUpMsg.toUserId.toString(),
                            toUserId = pickUpMsg.fromUserId.toString(),
                            freeCallDuration = pickUpMsg.callFreeSeconds,
                            rtcToken = pickUpMsg.rtcToken,
                        )
                        finish()
                    }
                } else {
                    finish()
                }
            }
        )
    }

    // Keep a reference to the Job to cancel it when the Activity is destroyed
    private var matchingTextJob: Job? = null

    private fun showText() {
        // Cancel any existing job to avoid multiple coroutines updating the text
        matchingTextJob?.cancel()
        matchingTextJob = lifecycleScope.launch {
            val baseText = "Matching"
            var dots = 1
            while (true) { // Loop indefinitely, or until the Job is cancelled
                binding.matchText.text = baseText + ".".repeat(dots)
                dots++
                if (dots > 3) {
                    dots = 1
                }
                delay(500) // Adjust the delay as needed (e.g., 500ms)
            }
        }
    }

    private var callHandler: Handler? = null
    private var call30sRunnable: Runnable? = null

    private fun startCallTimeouts() {
        callHandler = Handler(Looper.getMainLooper())
        call30sRunnable = Runnable {
            ToastUtils.showToast(CallmeApplication.context.getString(R.string.user_not_available_try_again))
            VideoCallManager.hangUp(
                scope = lifecycleScope,
                channelName = channelName.toString(),
                oppositeUserId = oppositeUserId ?: UserInfoManager.myUserInfo?.userId.toString(),
                hangUpReason = HangUpReason.NORMAL,
                remark = HangupScene.FLASH_CHAT_TIMEOUT,
            )
            finish()
        }
        callHandler?.postDelayed(call30sRunnable!!, 30_000)
    }

    private fun clearCallTimeouts() {
        call30sRunnable?.let { callHandler?.removeCallbacks(it) }
    }

    private fun startRingtone() {
        if (AppLifecycleManager.isAppInBackground()) {
            return
        }
        AudioPlayManager.playVideoRingtone(this)
    }

    private fun stopRingtone() {
        AudioPlayManager.stopPlay(this)
    }

    override fun onDestroy() {
        clearCallTimeouts()
        stopRingtone()
        super.onDestroy()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            // 拦截回退
            return true
        }
        return super.onKeyDown(keyCode, event)
    }
}
