package com.score.callmetest.ui.message.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.score.callmetest.CallStatus
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemFragmentFollowingBinding
import com.score.callmetest.databinding.ItemListBottomBinding
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.network.FollowModel
import com.score.callmetest.util.CountryUtils
import com.score.callmetest.util.click

/**
 * Message模块中Following页面的适配器
 * 专门用于Message Tab中的关注列表展示
 */
class MessageFollowingAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    
    companion object {
        private const val VIEW_TYPE_FOLLOWING = 0
        private const val VIEW_TYPE_BOTTOM = 1
    }
    
    private val followings = mutableListOf<FollowModel>()
    private var showBottom: Boolean = true
    
    // 状态缓存，用于实时更新在线状态（统一的状态数据源）
    private val statusCache = mutableMapOf<String, StatusInfo>()
    private val statusUpdateLock = Any()

    /**
     * 状态信息，包含状态值、时间戳和来源
     */
    data class StatusInfo(
        val status: String,
        val timestamp: Long,
        val source: String // "api" | "interface"
    )

    // 点击监听器
    private var onVideoCallClickListener: ((FollowModel) -> Unit)? = null
    private var onItemClickListener: ((FollowModel) -> Unit)? = null
    private var onAvatarClickListener: ((FollowModel) -> Unit)? = null
    
    fun setShowBottom(show: Boolean) {
        if (this.showBottom != show) {
            this.showBottom = show
            notifyDataSetChanged()
        }
    }
    
    fun setData(newFollowings: List<FollowModel>) {
        followings.clear()
        followings.addAll(newFollowings)

        // 同步更新状态缓存，将接口返回的状态也保存到statusCache中
        synchronized(statusUpdateLock) {
            val currentTime = System.currentTimeMillis()
            newFollowings.forEach { followModel ->
                if (followModel.userId.isNotBlank() && !followModel.onlineStatus.isNullOrBlank()) {
                    val existingStatus = statusCache[followModel.userId]
                    // 只有当缓存中没有更新的状态时，才使用接口返回的状态
                    if (existingStatus == null || existingStatus.timestamp < currentTime - 10_000) { // 10秒内的状态认为是新的
                        statusCache[followModel.userId] = StatusInfo(
                            status = followModel.onlineStatus ?: CallStatus.OFFLINE,
                            timestamp = currentTime,
                            source = "api"
                        )
                    }
                }
            }
        }

        notifyDataSetChanged()
    }
    
    fun setOnVideoCallClickListener(listener: (FollowModel) -> Unit) {
        this.onVideoCallClickListener = listener
    }
    
    fun setOnItemClickListener(listener: (FollowModel) -> Unit) {
        this.onItemClickListener = listener
    }
    
    fun setOnAvatarClickListener(listener: (FollowModel) -> Unit) {
        this.onAvatarClickListener = listener
    }
    
    /**
     * 更新用户状态（来自IMessageStatus接口）
     * @param statusMap 用户ID到状态的映射
     */
    fun updateStatus(statusMap: Map<String, String>) {
        updateStatusInternal(statusMap, "interface")
    }


    /**
     * 内部状态更新方法，支持指定来源
     * @param statusMap 用户ID到状态的映射
     * @param source 状态来源
     */
    private fun updateStatusInternal(statusMap: Map<String, String>, source: String) {
        val currentTime = System.currentTimeMillis()
        val changedUserIds = mutableListOf<String>()

        synchronized(statusUpdateLock) {
            statusMap.forEach { (userId, newStatus) ->
                val existingStatus = statusCache[userId]
                val shouldUpdate = when {
                    existingStatus == null -> true // 没有缓存，直接更新
                    existingStatus.status != newStatus -> true // 状态发生变化，需要更新
                    source == "interface" && existingStatus.source == "api" -> true // 接口状态比API状态新
                    source == "api" && existingStatus.source == "interface" -> false // API状态不能覆盖接口状态
                    currentTime - existingStatus.timestamp > 10_000 -> true // 超过10秒的状态认为过期，需要更新
                    else -> false
                }

                if (shouldUpdate) {
                    statusCache[userId] = StatusInfo(
                        status = newStatus,
                        timestamp = currentTime,
                        source = source
                    )
                    changedUserIds.add(userId)
                }
            }
        }

        // 只更新有状态变化的item
        changedUserIds.forEach { userId ->
            val position = followings.indexOfFirst { it.userId == userId }
            if (position != -1) {
                notifyItemChanged(position, "status")
            }
        }
    }
    
    /**
     * 获取所有用户ID，用于状态查询
     */
    fun getUserIds(): List<String> {
        return followings.map { it.userId }.filter { it.isNotBlank() }
    }

    /**
     * 获取用户当前显示的状态（供点击事件使用，确保状态一致性）
     * @param userId 用户ID
     * @return 当前显示的状态
     */
    fun getCurrentDisplayStatus(userId: String): String {
        synchronized(statusUpdateLock) {
            return statusCache[userId]?.status ?: CallStatus.OFFLINE
        }
    }

    /**
     * 清理过期的状态缓存（防止内存泄漏）
     * @param maxAgeMs 最大保留时间（毫秒）
     */
    fun cleanExpiredStatus(maxAgeMs: Long = 60_000) { // 默认1分钟
        synchronized(statusUpdateLock) {
            val currentTime = System.currentTimeMillis()
            val expiredKeys = statusCache.filter { (_, statusInfo) ->
                currentTime - statusInfo.timestamp > maxAgeMs
            }.keys
            expiredKeys.forEach { statusCache.remove(it) }
        }
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_FOLLOWING -> {
                val binding = ItemFragmentFollowingBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false)
                FollowingViewHolder(binding)
            }
            VIEW_TYPE_BOTTOM -> {
                val binding = ItemListBottomBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false)
                BottomViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }
    
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is FollowingViewHolder -> {
                if (position < followings.size) {
                    holder.bind(followings[position])
                }
            }
            is BottomViewHolder -> {
                holder.bind("Bottom")
            }
        }
    }
    
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int, payloads: MutableList<Any>) {
        if (payloads.isNotEmpty() && payloads[0] == "status" && holder is FollowingViewHolder) {
            // 只更新状态相关的UI
            if (position < followings.size) {
                holder.updateStatus(followings[position])
            }
        } else {
            super.onBindViewHolder(holder, position, payloads)
        }
    }
    
    override fun getItemCount(): Int = followings.size + if (showBottom) 1 else 0
    
    override fun getItemViewType(position: Int): Int {
        return if (showBottom && position == followings.size) VIEW_TYPE_BOTTOM else VIEW_TYPE_FOLLOWING
    }
    
    /**
     * Following项的ViewHolder
     */
    inner class FollowingViewHolder(private val binding: ItemFragmentFollowingBinding) : RecyclerView.ViewHolder(binding.root) {

        
        fun bind(followModel: FollowModel) {
            val context = itemView.context
            binding.apply {
                // 设置用户名
                tvUsername.text = followModel.nickname ?: ""

                // 设置国家信息
                tvRegion.text = followModel.country ?: ""
                ivFlag.setImageResource(CountryUtils.getIconByEnName(followModel.country))

                // 设置头像
                val avatarUrl = followModel.avatarUrl ?: followModel.avatarThumbUrl ?: ""
                if (avatarUrl.isNotEmpty()) {
                    Glide.with(context)
                        .load(avatarUrl)
                        .placeholder(R.drawable.placeholder)
                        .error(R.drawable.placeholder)
                        .into(ivAvatar)
                } else {
                    ivAvatar.setImageResource(R.drawable.placeholder)
                }
            }

            
            // 更新状态
            updateStatus(followModel)
            
            // 设置点击事件
            setupClickListeners(followModel)
        }
        
        fun updateStatus(followModel: FollowModel) {
            // 统一从statusCache获取状态，确保数据源一致性
            var status: String
            synchronized(statusUpdateLock) {
                status = statusCache[followModel.userId]?.status ?: CallStatus.OFFLINE
            }

            // 适配审核模式
            if (StrategyManager.isReviewPkg()) {
                // 简化审核模式处理，直接使用GlobalManager的方法
                status = GlobalManager.getReviewOtherStatus(followModel.userId)
            }

            // 设置状态指示器颜色
            val statusColor = GlobalManager.getStatusColor(status)
            GlobalManager.setViewRoundBackground(binding.statusIndicator, statusColor)

            // 根据状态设置按钮图标
            binding.ivVideoIndicator.visibility = View.VISIBLE
            if (status == CallStatus.ONLINE) {
                // 在线状态：显示视频通话图标
                binding.ivVideoIndicator.setImageResource(R.drawable.call_video)
            } else {
                // 其他状态：显示消息图标
                binding.ivVideoIndicator.setImageResource(R.drawable.btn_message)
            }
        }
        
        private fun setupClickListeners(followModel: FollowModel) {
            // 头像点击
            binding.ivAvatar.click {
                onAvatarClickListener?.invoke(followModel)
            }
            
            // 整个item点击
            itemView.click {
                onItemClickListener?.invoke(followModel)
            }
            
            // 视频通话按钮点击
            binding.ivVideoIndicator.click {
                onVideoCallClickListener?.invoke(followModel)
            }
        }
    }
    
    /**
     * 底部项的ViewHolder
     */
    inner class BottomViewHolder(private val binding: ItemListBottomBinding) : RecyclerView.ViewHolder(binding.root) {

        fun bind(text: String) {
            binding.tvBottomText.text = text
        }
    }
}
