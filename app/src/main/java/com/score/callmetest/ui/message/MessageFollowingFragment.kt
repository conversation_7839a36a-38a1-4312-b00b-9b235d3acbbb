package com.score.callmetest.ui.message

import android.Manifest
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentFollowContentBinding
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.manager.FollowManager
import com.score.callmetest.manager.SocketManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.network.FollowModel
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity
import com.score.callmetest.ui.message.adapter.MessageFollowingAdapter
import com.score.callmetest.ui.mine.follow.FollowViewModel
import com.score.callmetest.ui.videocall.VideoCallActivity
import com.score.callmetest.ui.widget.InsufficientBalanceDialog
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.ToastUtils
import timber.log.Timber

/**
 * Message模块中的关注列表页面
 * 使用网络API获取关注数据，通过IMessageStatus接口接收状态更新
 */
class MessageFollowingFragment : BaseFragment<FragmentFollowContentBinding, FollowViewModel>(), IMessageStatus {

    // recyclerview 相关
    private lateinit var mAdapter: MessageFollowingAdapter
    private val mLayoutManager by lazy { LinearLayoutManager(context) }
    
    // 是否需要滚动到顶部的标志
    private var mNeedScrollToTop = false

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentFollowContentBinding = FragmentFollowContentBinding.inflate(inflater, container, false)

    override fun getViewModelClass(): Class<FollowViewModel> = FollowViewModel::class.java

    override fun initView() {
        super.initView()
        // 初始化空视图，避免后续 ViewStub 问题
        if (binding.emptyView.parent != null) {
            binding.emptyView.inflate()
        }
        binding.emptyView.visibility = View.GONE

        setupRecyclerView()
        setupSwipeRefresh()
    }
    
    private fun setupRecyclerView() {
        mAdapter = MessageFollowingAdapter()
        binding.recyclerView.apply {
            adapter = mAdapter
            layoutManager = mLayoutManager

            // 添加滚动监听，实现加载更多
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    // 只有向下滚动时才检查
                    if (dy > 0) {
                        val visibleItemCount = mLayoutManager.childCount
                        val totalItemCount = mLayoutManager.itemCount
                        val firstVisibleItemPosition = mLayoutManager.findFirstVisibleItemPosition()

                        // 当滚动到倒数第5个item时开始加载更多
                        if (!viewModel.isFollowingLoadingMore() &&
                            viewModel.hasMoreFollowingData() &&
                            (visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 5) {
                            loadMoreFollowing()
                        }
                    }
                }
            })
        }
        
        // 设置适配器监听器
        setupAdapterListeners()
    }
    
    private fun setupAdapterListeners() {
        // 设置头像点击监听器
        mAdapter.setOnAvatarClickListener { followModel ->
            handleAvatarClick(followModel)
        }
        
        // 设置item点击监听器
        mAdapter.setOnItemClickListener { followModel ->
            handleItemClick(followModel)
        }
        
        // 设置视频通话点击监听器
        mAdapter.setOnVideoCallClickListener { followModel ->
            handleVideoCallClick(followModel)
        }
    }

    /**
     * 更新底部项显示状态
     */
    private fun updateBottomItemVisibility() {
        binding.recyclerView.post {
            val dataCount = viewModel.followingList.value?.size ?: 0
            val shouldShowBottom = dataCount > 0
            mAdapter.setShowBottom(shouldShowBottom)
        }
    }
    
    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.apply {
            setColorSchemeResources(R.color.refresh_loading)
            // 下拉刷新
            setOnRefreshListener {
                // 显示刷新动画
                isRefreshing = true
                // 设置需要滚动到顶部的标志
                mNeedScrollToTop = true
                // 重新加载数据（刷新）
                loadFollowingList(isRefresh = true)
            }
        }
    }

    override fun initData() {
        super.initData()
        setupObservers()

        // 显示下拉刷新状态并加载数据
        binding.swipeRefreshLayout.isRefreshing = true
        mNeedScrollToTop = true
        loadFollowingList(isRefresh = true)
    }

    private fun loadFollowingList(isRefresh: Boolean = true) {
        // 调用ViewModel加载关注列表
        viewModel.loadFollowingList(isRefresh)
    }

    private fun loadMoreFollowing() {
        // 加载更多关注数据
        viewModel.loadFollowingList(isRefresh = false)
    }
    
    private fun setupObservers() {
        // 监听关注和取消关注事件，当变化时重新加载列表
        EventBus.observe(this, FollowManager.FollowListRefreshEvent::class.java) { event ->
            // 当关注数量变化时，重新加载关注列表
            loadFollowingList()
        }

        // 观察关注列表数据变化
        viewModel.followingList.observe(viewLifecycleOwner) { followingList ->
            // 显示/隐藏空视图
            emptyView(followingList.isNullOrEmpty())

            // 提交新列表前保存当前滚动位置
            val firstVisiblePosition = mLayoutManager.findFirstVisibleItemPosition()

            // 更新适配器数据
            mAdapter.setData(followingList ?: emptyList())

            // 数据加载完成后，停止刷新动画
            binding.swipeRefreshLayout.isRefreshing = false

            // 只有在需要滚动到顶部时才滚动（下拉刷新时）
            if (mNeedScrollToTop) {
                binding.recyclerView.scrollToPosition(0)
                mNeedScrollToTop = false
            } else if (firstVisiblePosition == 0) {
                // 如果原本就在顶部，确保仍然在顶部
                binding.recyclerView.scrollToPosition(0)
            }

            // 列表更新后检查底部项显示状态
            updateBottomItemVisibility()
        }
        
        // 观察加载状态 - 只有在刷新时才显示顶部加载动画
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            // 只有在不是加载更多的情况下才显示顶部加载动画
            if (!viewModel.isFollowingLoadingMore()) {
                binding.swipeRefreshLayout.isRefreshing = isLoading
                if (isLoading) {
                    mNeedScrollToTop = true
                }
            }
        }
        
        // 观察错误信息
        viewModel.errorMessage.observe(viewLifecycleOwner) { errorMessage ->
            if (!errorMessage.isNullOrEmpty()) {
                Timber.tag("MessageFollowFragment").e("Error loading following list: $errorMessage")
                // 错误发生时，停止刷新动画
                binding.swipeRefreshLayout.isRefreshing = false
            }
            emptyView(viewModel.followingList.value.isNullOrEmpty())
        }
    }

    /**
     * 显示/隐藏EmptyView
     */
    private fun emptyView(isEmpty: Boolean) {
        if (isEmpty) {
            // 显示空页面
            binding.emptyView.visibility = View.VISIBLE
            binding.recyclerView.visibility = View.GONE
        } else {
            // 显示列表，隐藏空页面
            binding.emptyView.visibility = View.GONE
            binding.recyclerView.visibility = View.VISIBLE
        }
    }
    
    // ==================== 点击事件处理 ====================
    
    private fun handleAvatarClick(followModel: FollowModel) {
        //Timber.tag("MessageFollowingFragment").d("Avatar clicked: ${followModel.nickname}")

        // 跳转到主播资料详情页面
        val intent = Intent(activity, BroadcasterDetailActivity::class.java)
        intent.putExtra("broadcaster_model", followModelToBroadcasterModel(followModel))
        startActivity(intent)
    }

    private fun handleItemClick(followModel: FollowModel) {
        //Timber.tag("MessageFollowingFragment").d("Item clicked: ${followModel.nickname}")

        // 跳转到主播资料详情页面
        val intent = Intent(activity, BroadcasterDetailActivity::class.java)
        intent.putExtra("broadcaster_model", followModelToBroadcasterModel(followModel))
        startActivity(intent)
    }
    
    private fun handleVideoCallClick(followModel: FollowModel) {
        //Timber.tag("MessageFollowingFragment").d("Video call/message clicked: ${followModel.nickname}")

        // 使用适配器提供的当前显示状态，确保状态一致性
        val currentStatus = mAdapter.getCurrentDisplayStatus(followModel.userId)

        if (currentStatus == CallStatus.ONLINE) {
            // 在线状态：发起视频通话
            startVideoCall(followModel)
        } else {
            // 其他状态：跳转到聊天
            startChat(followModel)
        }
    }

    /**
     * 发起视频通话
     */
    private fun startVideoCall(followModel: FollowModel) {
        //Timber.tag("MessageFollowingFragment").d("Starting video call with: ${followModel.nickname}")

        // 转换为BroadcasterModel获取价格信息
        val broadcasterModel = followModelToBroadcasterModel(followModel)
        val unitPrice = broadcasterModel.callCoins ?: 0

        // 检查价格是否有效
        if (unitPrice < 0) {
            ToastUtils.showShortToast("Invalid price")
            return
        }

        if (!StrategyManager.isReviewPkg()) {
            // 判断金币是否足够
            val availableCoins = UserInfoManager.myUserInfo?.availableCoins ?: 0
            if (availableCoins < unitPrice) {
                // 弹出金币充值弹窗
                val dialog = InsufficientBalanceDialog.newInstance(unitPrice, RechargeSource.FOLLOW_CALL.value())
                dialog.show(parentFragmentManager, "insufficient_balance")
                return
            }
        }

        // 检查网络连接
        if (!SocketManager.instance.isConnected()) {
            ToastUtils.showToast(requireContext().getString(R.string.long_connection_network_offline))
            return
        }

        // 获取在线状态并发起通话
        UserInfoManager.loadOnlineStatus(
            lifecycleScope, followModel.userId
        ) { status, error ->
            activity?.runOnUiThread {
                if (error == null && status != null) {
                    if (CallStatus.ONLINE != status && CallStatus.AVAILABLE != status) {
                        // 状态不可用时显示toast
                        val statusText = CallStatus.getDisplayText(status)
                        val message = getString(R.string.user_status_not_available, statusText)
                        ToastUtils.showToast(message)
                        return@runOnUiThread
                    }

                    // 在线状态检查通过后，请求权限
                    AppPermissionManager.checkAndRequestCameraMicrophonePermission(
                        requireActivity() as AppCompatActivity,
                        onGranted = {
                            VideoCallActivity.startOutgoing(
                                context = requireActivity(),
                                userId = followModel.userId,
                                avatarUrl = followModel.avatarUrl ?: followModel.avatarThumbUrl ?: "",
                                nickname = followModel.nickname ?: "",
                                age = "", // Following列表中没有年龄信息
                                country = followModel.country ?: "",
                                unitPrice = unitPrice.toString()
                            )
                        },
                        onDenied = {
                            ToastUtils.showToast(requireContext().getString(R.string.camera_microphone_permission_required))
                            val shouldShow = AppPermissionManager.shouldShowRequestPermissionRationale(
                                requireActivity() as AppCompatActivity,
                                Manifest.permission.RECORD_AUDIO
                            )
                            if (!shouldShow) {
                                // 权限被永久拒绝，跳转设置页面
                                ToastUtils.showToast(CallmeApplication.context.getString(R.string.camera_microphone_permission_check_hint))
                                AppPermissionManager.openAppSettings(requireActivity() as AppCompatActivity)
                            }
                        }
                    )
                } else {
                    ToastUtils.showToast(requireContext().getString(R.string.failed_to_get_user_status))
                }
            }
        }
    }

    /**
     * 跳转到聊天
     */
    private fun startChat(followModel: FollowModel) {
        //Timber.tag("MessageFollowingFragment").d("Starting chat with: ${followModel.nickname}")

        viewModel.gotoChat(followModel.userId)
    }

    // ==================== IMessageStatus 接口实现 ====================

    override fun provideUserIds(): List<String> {
        // 返回当前关注列表中所有用户的ID，用于状态查询
        return mAdapter.getUserIds()
    }

    override fun notifyStatusChanged(statusMap: Map<String, String>) {
        // 接收来自MessageFragment的状态更新，更新适配器中的状态显示
        mAdapter.updateStatus(statusMap)
       // Timber.tag("MessageFollowingFragment").d("Status updated for ${statusMap.size} users")
    }

    override fun onResume() {
        super.onResume()
        // 页面可见时检查底部项显示状态
        updateBottomItemVisibility()
        // 清理过期的状态缓存
        mAdapter.cleanExpiredStatus()
    }

    /**
     * 将FollowModel转换为BroadcasterModel
     */
    private fun followModelToBroadcasterModel(followModel: FollowModel): BroadcasterModel {
        return BroadcasterModel(
            userId = followModel.userId,
            nickname = followModel.nickname,
            avatar = followModel.avatar,
            gender = followModel.gender,
            age = followModel.age,
            country = followModel.country,
            status = followModel.onlineStatus ?: CallStatus.OFFLINE,
            callCoins = followModel.unitPrice,
            unit = "min",
            isFriend = followModel.mutualFlow,
            about = followModel.about,
            grade = followModel.level,
            analysisLanguage = followModel.language,
            isSignBroadcaster = followModel.isSignBroadcaster,
            showRoomVersion = followModel.showRoomVersion,
            broadcasterType = followModel.userType,
            avatarThumbUrl = followModel.avatarThumbUrl,
            isVip = followModel.isVip ?: false,
        )
    }
}
