package com.score.callmetest.ui.message

import android.graphics.Typeface
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentMessageBinding
import com.score.callmetest.manager.FlashChatManager
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.util.ClickUtils
import timber.log.Timber

/**
 * 消息主页面，包含Messages和Call两个Tab
 */
class MessageFragment : BaseFragment<FragmentMessageBinding, MessageViewModel>() {

    private lateinit var mPagerAdapter: MessagePagerAdapter

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentMessageBinding {
        return FragmentMessageBinding.inflate(inflater, container, false)
    }

    override fun getViewModelClass() = MessageViewModel::class.java

    override fun initView() {
        setupTabLayout()
        setupViewPager()
    }

    override fun initData() {
        super.initData()
        setupObservers()
    }

    private fun setupObservers() {
        viewModel.lastStatusMap.observe(viewLifecycleOwner) {
            // 遍历子fragment更新在线状态
            childFragmentManager.fragments.forEach { fragment ->
                if (fragment is IMessageStatus) {
                    fragment.notifyStatusChanged(it)
                }
            }

        }
    }


    private fun setupTabLayout() {
        with(binding.tabMessage) {
            tabRippleColor = null
            // 添加Messages标签
            addTab(newTab().apply {
                customView = createCustomTabView("Messages", selected = true)
            })

            // 添加Following标签
            addTab(newTab().apply {
                customView = createCustomTabView("Followed", selected = false)
            })

            // 添加Call标签
            addTab(newTab().apply {
                customView = createCustomTabView("Calls", selected = false)
            })


        }
    }

    private fun setupViewPager() {
        // 初始化ViewPager适配器
        mPagerAdapter = MessagePagerAdapter(this)

        // 配置ViewPager
        binding.viewPager.apply {
            adapter = mPagerAdapter
            offscreenPageLimit = 3 // 预加载页面数量，现在有3个Tab
        }
    }


    override fun initListener() {
        super.initListener()
        // click-clean-一键已读
        ClickUtils.setOnIsolatedClickListener(binding.btnClear) {
            if (binding.viewPager.currentItem == 0) {
                (childFragmentManager.fragments[0] as? MsgListFragment)?.clearMsg()
            }
        }

        // 监听Tab切换
        binding.tabMessage.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                updateTabStyle(tab.customView, true)
                binding.viewPager.currentItem = tab.position
                binding.btnClear.visibility =
                    if (tab.position == 0) View.VISIBLE else View.INVISIBLE

                // Tab切换时触发状态更新
                triggerStatusUpdate()
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {
                updateTabStyle(tab.customView, false)
            }

            override fun onTabReselected(tab: TabLayout.Tab) {
                // 不需要处理
            }
        })

        // 监听ViewPager页面切换
        binding.viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                binding.tabMessage.getTabAt(position)?.select()
            }
        })
    }

    /**
     * 创建自定义Tab视图
     * @param text Tab文本
     * @param customView 现有的自定义视图（如果有）
     * @param selected 是否选中
     * @return 自定义Tab视图
     */
    private fun createCustomTabView(
        text: String? = null,
        customView: View? = null,
        selected: Boolean = false
    ): View {
        val view = customView ?: layoutInflater.inflate(R.layout.tab_custom, null)
        updateTabStyle(view, selected, text)
        return view
    }

    /**
     * 更新Tab样式
     * @param view Tab视图
     * @param selected 是否选中
     * @param text Tab文本（可选）
     */
    private fun updateTabStyle(view: View?, selected: Boolean, text: String? = null) {
        if (view == null) return

        val textView = view.findViewById<TextView>(R.id.tab_text)
        val indicator = view.findViewById<View>(R.id.tab_indicator)

        textView?.setTypeface(
            Typeface.create(
                "sans-serif",
                if (selected) Typeface.BOLD else Typeface.NORMAL
            )
        );

        // 设置文本（如果提供）
        if (!text.isNullOrEmpty()) {
            textView.text = text
        }

        // 设置样式
        textView.textSize = if (selected) 22f else 15f
        textView.paint.isFakeBoldText = selected

        // 设置指示器
        indicator.visibility = if (selected) View.VISIBLE else View.GONE

        // 如果选中，调整指示器宽度
        if (selected) {
            textView.post {
                indicator.layoutParams.width = textView.width / 2
                indicator.layoutParams.height = textView.height / 2
                indicator.requestLayout()
            }
        }
    }

    //状态更新方法
    private fun triggerStatusUpdate() {
        // 从所有子fragment中提起userIds
        val providedList = arrayListOf<String>()
        childFragmentManager.fragments.forEach {
            if (it is IMessageStatus) {
                providedList.addAll(it.provideUserIds())
            }
        }
        // 过滤空字符串并去重
        val userIds = providedList.filter { it.isNotBlank() }.distinct()
        viewModel.checkOnlineStatus(userIds)
    }

    /**
     * ViewPager适配器
     */
    private inner class MessagePagerAdapter(fragment: Fragment) : FragmentStateAdapter(fragment) {
        override fun getItemCount(): Int = binding.tabMessage.tabCount

        override fun createFragment(position: Int): Fragment {
            return when (position) {
                0 -> MsgListFragment()
                1 -> MessageFollowingFragment()
                2 -> CallHistoryFragment()
                else -> throw IllegalArgumentException("Invalid position: $position")
            }
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel.startStatusUpdates {
            triggerStatusUpdate()
        }
        FlashChatManager.startShowDialogTimer()
    }

    override fun onPause() {
        super.onPause()
        // 停止状态更新
        viewModel.stopStatusUpdates()
        FlashChatManager.stopShowDialogTimer()
        Timber.tag("MessageFragment").d("onPause:")
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        Timber.tag("MessageFragment").d("onHiddenChanged: hidden=$hidden")
        if (hidden) {
            FlashChatManager.stopShowDialogTimer()
        } else {
            FlashChatManager.startShowDialogTimer()
        }
    }
}