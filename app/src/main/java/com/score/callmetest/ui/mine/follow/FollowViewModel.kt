package com.score.callmetest.ui.mine.follow

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.score.callmetest.manager.FollowManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.FollowModel
import com.score.callmetest.network.UserInfo
import com.score.callmetest.ui.base.BaseViewModel
import com.score.callmetest.util.EventBus


class FollowViewModel : BaseViewModel() {
    // 关注列表数据（type=3）
    private val _followingList = MutableLiveData<List<FollowModel>>()
    val followingList: LiveData<List<FollowModel>> = _followingList

    // 粉丝列表数据（type=2）
    private val _followersList = MutableLiveData<List<FollowModel>>()
    val followersList: LiveData<List<FollowModel>> = _followersList

    // 互关列表数据（type=1）
    private val _friendsList = MutableLiveData<List<FollowModel>>()
    val friendsList: LiveData<List<FollowModel>> = _friendsList

    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    // 错误信息
    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage

    // 跳转到聊天页面的用户信息
    private val _goChatUser = MutableLiveData<UserInfo>()
    val goChatUser: LiveData<UserInfo> = _goChatUser

    // 分页状态管理
    private var followingCurrentPage = 1
    private var followersCurrentPage = 1
    private var isFollowingLoadingMore = false
    private var isFollowersLoadingMore = false
    private var hasMoreFollowing = true
    private var hasMoreFollowers = true
    private val pageSize = 15

    // 当前列表数据
    private val currentFollowingList = mutableListOf<FollowModel>()
    private val currentFollowersList = mutableListOf<FollowModel>()

    /**
     * 加载关注列表（type=3）
     * @param isRefresh 是否是刷新操作，true表示刷新，false表示加载更多
     */
    fun loadFollowingList(isRefresh: Boolean = true) {
        if (isRefresh) {
            // 刷新操作，重置分页状态
            followingCurrentPage = 1
            hasMoreFollowing = true
            currentFollowingList.clear()
        } else {
            // 加载更多操作，检查是否正在加载或没有更多数据
            if (isFollowingLoadingMore || !hasMoreFollowing) {
                return
            }
            followingCurrentPage++
        }

        isFollowingLoadingMore = true
        FollowManager.loadFollowingList(pageSize, followingCurrentPage, object : FollowManager.FollowPageCallback {
            override fun onSuccess(list: List<FollowModel>) {
                isFollowingLoadingMore = false

                if (isRefresh) {
                    currentFollowingList.clear()
                }
                currentFollowingList.addAll(list)

                // 判断是否还有更多数据
                hasMoreFollowing = list.size >= pageSize

                _followingList.postValue(currentFollowingList.toList())
            }

            override fun onError(errorMsg: String) {
                isFollowingLoadingMore = false
                if (!isRefresh) {
                    // 加载更多失败时，回退页码
                    followingCurrentPage--
                }
                _errorMessage.postValue(errorMsg)
            }

            override fun onLoading(isLoading: Boolean) {
                _isLoading.postValue(isLoading)
            }
        })
    }

    /**
     * 加载粉丝列表（type=2）
     * @param isRefresh 是否是刷新操作，true表示刷新，false表示加载更多
     */
    fun loadFollowersList(isRefresh: Boolean = true) {
        if (isRefresh) {
            // 刷新操作，重置分页状态
            followersCurrentPage = 1
            hasMoreFollowers = true
            currentFollowersList.clear()
        } else {
            // 加载更多操作，检查是否正在加载或没有更多数据
            if (isFollowersLoadingMore || !hasMoreFollowers) {
                return
            }
            followersCurrentPage++
        }

        isFollowersLoadingMore = true
        FollowManager.loadFollowersList(pageSize, followersCurrentPage, object : FollowManager.FollowPageCallback {
            override fun onSuccess(list: List<FollowModel>) {
                isFollowersLoadingMore = false

                if (isRefresh) {
                    currentFollowersList.clear()
                }
                currentFollowersList.addAll(list)

                // 判断是否还有更多数据
                hasMoreFollowers = list.size >= pageSize

                _followersList.postValue(currentFollowersList.toList())

                // 当是刷新操作时，通知MineFragment更新关系计数
                if (isRefresh) {
                    EventBus.post(FollowManager.FollowListRefreshEvent())
                }
            }

            override fun onError(errorMsg: String) {
                isFollowersLoadingMore = false
                if (!isRefresh) {
                    // 加载更多失败时，回退页码
                    followersCurrentPage--
                }
                _errorMessage.postValue(errorMsg)
            }

            override fun onLoading(isLoading: Boolean) {
                _isLoading.postValue(isLoading)
            }
        })
    }


    /**
     * 关注用户（添加好友）
     */
    fun followUser(userId: String, callback: (Boolean) -> Unit) {
        FollowManager.followUser(userId, object : FollowManager.FollowActionCallback {
            override fun onSuccess() {
                callback(true)
            }

            override fun onError(errorMsg: String) {
                _errorMessage.postValue(errorMsg)
                callback(false)
            }
        })
    }

    /**
     * 取关用户（删除好友）
     */
    fun unfollowUser(userId: String, callback: (Boolean) -> Unit) {
        FollowManager.unfollowUser(userId, object : FollowManager.FollowActionCallback {
            override fun onSuccess() {
                callback(true)
            }

            override fun onError(errorMsg: String) {
                _errorMessage.postValue(errorMsg)
                callback(false)
            }
        })
    }

    /**
     * 点击item查询userinfo后跳转到聊天
     */
    fun gotoChat(userId: String) {
        // 先查缓存，再网络查询
        UserInfoManager.getUserInfo(userId) { getUserInfo ->
            getUserInfo?.let { nonNullUser ->
                _goChatUser.value = nonNullUser
            }
        }
    }

    /**
     * 检查关注列表是否还有更多数据
     */
    fun hasMoreFollowingData(): Boolean = hasMoreFollowing

    /**
     * 检查粉丝列表是否还有更多数据
     */
    fun hasMoreFollowersData(): Boolean = hasMoreFollowers

    /**
     * 检查关注列表是否正在加载更多
     */
    fun isFollowingLoadingMore(): Boolean = isFollowingLoadingMore

    /**
     * 检查粉丝列表是否正在加载更多
     */
    fun isFollowersLoadingMore(): Boolean = isFollowersLoadingMore
}