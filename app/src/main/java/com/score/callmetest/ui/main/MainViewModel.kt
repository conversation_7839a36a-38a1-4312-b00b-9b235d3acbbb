package com.score.callmetest.ui.main

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.score.callmetest.manager.AppConfigManager
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.network.LastSpecialOfferResponse
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.PromotionGoodsRequest
import com.score.callmetest.network.RcTokenRequest
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.SpecialOfferRequest
import com.score.callmetest.network.UploadRiskInfoReqs
import com.score.callmetest.ui.base.BaseViewModel
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.logAsTag
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber

class MainViewModel : BaseViewModel() {

    // ========== fab1促销弹窗相关 ==========
    /**
     * 活动促销信息 LiveData
     */
    val promotionModelLiveData = MutableLiveData<LastSpecialOfferResponse?>()
    val promotionLoading = MutableLiveData<Boolean>()

    /**
     * 新人促销信息 LiveData
     */
    val presentedCoinsLiveData = MutableLiveData<GoodsInfo?>()
    val presentedCoinsLoading = MutableLiveData<Boolean>()

    // ========== 原有功能 ==========
    /**
     * 融云token
     */
    fun getRongToken(
        onSuccess: (String?) -> Unit,
        onError: (Exception) -> Unit
    ) {
        viewModelScope.launch {
            try {
                val response = withContext(Dispatchers.IO) {
                    val request =
                        RcTokenRequest(AppConfigManager.getConfigValue("rc_app_key") ?: "")
                    RetrofitUtils.dataRepository.getRongCloudToken(request)
                }
                if (response is NetworkResult.Success) {
                    onSuccess(response.data)
                }
            } catch (e: Exception) {
                onError(e)
            }
        }
    }

    fun uploadRiskInfo() {
        val delay = AppConfigManager.getRiskControlConfig()?.k_interval ?: 5
        uploadRiskInfoInner(
            delay = delay,
            onError = {
                // 失败一次10s后再试一次
                uploadRiskInfoInner(10)
            }
        )
    }

    private fun uploadRiskInfoInner(
        delay: Int = 5,
        onSuccess: ((Boolean?) -> Unit)? = null,
        onError: ((Exception) -> Unit)? = null
    ) {
        val info = getRiskInfo()
        ThreadUtils.runOnIODelayed(delay * 1000L) {
            try {
                val response = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.uploadRiskInfo(
                        UploadRiskInfoReqs(
                            info = info,
                        )
                    )
                }
                if (response is NetworkResult.Success) {
                    onSuccess?.invoke(response.data)
                }
            } catch (e: Exception) {
                onError?.invoke(e)
            }
        }
    }

    // ========== fab1促销弹窗相关方法 ==========

    /**
     * 获取活动促销信息（从接口获取），结果通过 promotionModelLiveData 传递给 UI 层
     * @param request SpecialOfferRequest（可选，默认 payChannel="GP"）
     */
    suspend fun getPromotionList(request: SpecialOfferRequest = SpecialOfferRequest(bizVer = null, invitationId = null, payChannel = "GP")) {
        promotionLoading.postValue(true)
        try {
            val response = RetrofitUtils.dataRepository.getLastSpecialOfferV2(request)
            if (response is NetworkResult.Success) {
                response.data.toString().logAsTag(this.javaClass.name + " getPromotionList: ")
                if (!response.data.isNullOrEmpty()) {
                    promotionModelLiveData.postValue(response.data[0])
                } else {
                    promotionModelLiveData.postValue(null)
                }
            } else {
                promotionModelLiveData.postValue(null)
                Timber.d("获取促销信息失败--$response")
            }
        } catch (e: Exception) {
            promotionModelLiveData.postValue(null)
            Timber.tag(this.javaClass.name).e(e, "getPromotionList exception")
        } finally {
            promotionLoading.postValue(false)
        }
    }

    /**
     * 获取活动促销信息（供UI层调用，自动处理协程和LiveData）
     */
    fun fetchPromotion(request: SpecialOfferRequest = SpecialOfferRequest(bizVer = null, invitationId = null, payChannel = "GP")) {
        promotionLoading.value = true
        viewModelScope.launch {
            try {
                getPromotionList(request)
            } finally {
                promotionLoading.value = false
            }
        }
    }

    /**
     * 获取新人促销宝箱（供UI层调用，自动处理协程和LiveData）
     */
    fun fetchPresentedCoins(request: PromotionGoodsRequest = PromotionGoodsRequest()) {
        presentedCoinsLoading.value = true
        viewModelScope.launch {
            try {
                val response = RetrofitUtils.dataRepository.getPromotionGoods(request)
                if (response is NetworkResult.Success) {
                    presentedCoinsLiveData.value = response.data
                    Timber.d(" fetchPresentedCoins: ")

                    // 如果没有新人促销数据，自动获取活动促销数据
                    if (response.data == null) {
                        fetchPromotion()
                    }
                } else {
                    Timber.d(" fetchPresentedCoins error: $response")
                    presentedCoinsLiveData.value = null
                    // 获取失败时也尝试获取活动促销数据
                    fetchPromotion()
                }
            } catch (e: Exception) {
                presentedCoinsLiveData.value = null
                // 异常时也尝试获取活动促销数据
                fetchPromotion()
            } finally {
                presentedCoinsLoading.value = false
            }
        }
    }
}