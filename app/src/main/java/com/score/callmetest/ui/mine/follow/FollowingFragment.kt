package com.score.callmetest.ui.mine.follow

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentFollowContentBinding
import com.score.callmetest.manager.FollowManager
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.chat.ChatActivity
import com.score.callmetest.ui.mine.follow.adapter.FollowingAdapter
import com.score.callmetest.util.EventBus
import timber.log.Timber

/**
 * 关注列表页面
 * 展示用户的关注列表
 */
class FollowingFragment : BaseFragment<FragmentFollowContentBinding, FollowViewModel>() {

    // recyclerview 相关
    private lateinit var mAdapter: FollowingAdapter
    private val mLayoutManager by lazy { LinearLayoutManager(context) }
    
    // 是否需要滚动到顶部的标志
    private var mNeedScrollToTop = false

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentFollowContentBinding = FragmentFollowContentBinding.inflate(inflater, container, false)

    override fun getViewModelClass(): Class<FollowViewModel> = FollowViewModel::class.java

    override fun initView() {
        super.initView()
        // 初始化空视图，避免后续 ViewStub 问题
        if (binding.emptyView.parent != null) {
            binding.emptyView.inflate()
        }
        binding.emptyView.visibility = View.GONE

        setupRecyclerView()
        setupSwipeRefresh()
    }
    
    private fun setupRecyclerView() {
        mAdapter = FollowingAdapter()

        // 设置聊天点击监听器
        mAdapter.setOnChatClickListener { followModel ->
            // 跳转到聊天页面
            viewModel.gotoChat(followModel.userId)
        }

        binding.recyclerView.apply {
            adapter = mAdapter
            layoutManager = mLayoutManager

            // 添加滚动监听，实现加载更多
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    // 只有向下滚动时才检查
                    if (dy > 0) {
                        val visibleItemCount = mLayoutManager.childCount
                        val totalItemCount = mLayoutManager.itemCount
                        val firstVisibleItemPosition = mLayoutManager.findFirstVisibleItemPosition()

                        // 当滚动到倒数第5个item时开始加载更多
                        if (!viewModel.isFollowingLoadingMore() &&
                            viewModel.hasMoreFollowingData() &&
                            (visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 5) {
                            loadMoreFollowing()
                        }
                    }
                }
            })
        }
    }

    /**
     * 更新底部项显示状态
     */
    private fun updateBottomItemVisibility() {
        binding.recyclerView.post {
            // 修改逻辑：
            // 1. 列表为空时不显示bottom
            // 2. 只要有数据就显示bottom
            // 3. 加载更多时需要显示bottom
            val dataCount = viewModel.followingList.value?.size ?: 0
            val shouldShowBottom = dataCount > 0
            mAdapter.setShowBottom(shouldShowBottom)
        }
    }
    
    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.apply {
            setColorSchemeResources(R.color.refresh_loading)
            // 下拉刷新
            setOnRefreshListener {
                // 显示刷新动画
                isRefreshing = true
                // 设置需要滚动到顶部的标志
                mNeedScrollToTop = true
                // 重新加载数据（刷新）
                loadFollowingList(isRefresh = true)
            }
        }
    }

    override fun initData() {
        super.initData()
        setupObservers()

        // 显示下拉刷新状态并加载数据
        binding.swipeRefreshLayout.isRefreshing = true
        mNeedScrollToTop = true
        loadFollowingList(isRefresh = true)
    }

    private fun loadFollowingList(isRefresh: Boolean = true) {
        // 调用ViewModel加载关注列表
        viewModel.loadFollowingList(isRefresh)
    }

    private fun loadMoreFollowing() {
        // 加载更多关注数据
        viewModel.loadFollowingList(isRefresh = false)
    }
    
    private fun setupObservers() {
        // 监听关注和取消关注事件，当变化时重新加载列表
        EventBus.observe(this, FollowManager.FollowListRefreshEvent::class.java) { event ->
            // 当关注数量变化时，重新加载关注列表
            loadFollowingList()
        }

        // 观察关注列表数据变化
        viewModel.followingList.observe(viewLifecycleOwner) { followingList ->
            // 显示/隐藏空视图
            emptyView(followingList.isNullOrEmpty())

            // 提交新列表前保存当前滚动位置
            val firstVisiblePosition = mLayoutManager.findFirstVisibleItemPosition()

            // 更新适配器数据
            mAdapter.setData(followingList ?: emptyList())

            // 数据加载完成后，停止刷新动画
            binding.swipeRefreshLayout.isRefreshing = false

            // 只有在需要滚动到顶部时才滚动（下拉刷新时）
            if (mNeedScrollToTop) {
                binding.recyclerView.scrollToPosition(0)
                mNeedScrollToTop = false
            } else if (firstVisiblePosition == 0) {
                // 如果原本就在顶部，确保仍然在顶部
                binding.recyclerView.scrollToPosition(0)
            }

            // 列表更新后检查底部项显示状态
            updateBottomItemVisibility()
        }
        
        // 观察加载状态 - 只有在刷新时才显示顶部加载动画
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            // 只有在不是加载更多的情况下才显示顶部加载动画
            if (!viewModel.isFollowingLoadingMore()) {
                binding.swipeRefreshLayout.isRefreshing = isLoading
                if (isLoading) {
                    mNeedScrollToTop = true
                }
            }
        }
        
        // 观察错误信息
        viewModel.errorMessage.observe(viewLifecycleOwner) { errorMessage ->
            if (!errorMessage.isNullOrEmpty()) {
                Timber.tag("FollowingFragment").e("Error loading following list: $errorMessage")
                // 错误发生时，停止刷新动画
                binding.swipeRefreshLayout.isRefreshing = false
            }
            emptyView(viewModel.followingList.value.isNullOrEmpty())
        }

        // 观察跳转到聊天页面的用户信息
        viewModel.goChatUser.observe(viewLifecycleOwner) { user ->
            ChatActivity.start(context, user)
        }

    }

    /**
     * 显示/隐藏EmptyView
     */
    private fun emptyView(isEmpty: Boolean) {
        if (isEmpty) {
            // 显示空页面
            binding.emptyView.visibility = View.VISIBLE
            binding.recyclerView.visibility = View.GONE
        } else {
            // 显示列表，隐藏空页面
            binding.emptyView.visibility = View.GONE
            binding.recyclerView.visibility = View.VISIBLE
        }
    }

    override fun onResume() {
        super.onResume()
        // 页面可见时检查底部项显示状态
        updateBottomItemVisibility()

    }
} 