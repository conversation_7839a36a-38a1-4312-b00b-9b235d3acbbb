package com.score.callmetest.ui.widget

import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.graphics.drawable.toDrawable
import androidx.core.graphics.toColorInt
import androidx.fragment.app.DialogFragment
import com.opensource.svgaplayer.SVGAParser
import com.score.callmetest.R
import com.score.callmetest.manager.CountdownManager
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.TimeUtils
import com.score.callmetest.util.click
import kotlinx.coroutines.Job
import timber.log.Timber

class PromotionDialogFragment : DialogFragment() {

    private var amount: String = ""
    private var addAmount: String = ""
    private var description: String = ""
    private var price: Double = 0.0
    private var oldPrice: Double = 0.0
    private var buttonSvgaName: String = "sweep_button.svga"
    private var buttonEffectSvgaName: String = "tags_lucky.svga"
    private var remainingCount: Int = 0
    private var onButtonClickListener: (() -> Unit)? = null
    private var layoutResId: Int = R.layout.dialog_promotion
    private var treasureBoxImageUrl: String? = null

    // CountdownManager相关属性
    private var activityType: CountdownManager.ActivityType? = null
    private var activityId: String? = null

    // 倒计时相关
    private var countdownJob: Job? = null

    // 保存监听者引用，用于移除
    private var onTickListener: ((Long) -> Unit)? = null
    private var onFinishListener: (() -> Unit)? = null

    companion object {
        fun newInstance(
            layoutResId: Int = R.layout.dialog_promotion,
            amount: String,
            description: String,
            price: Double = 0.0,
            originPrice: Double = 0.0,
            buttonSvgaName: String = "sweep_button.svga",
            buttonEffectSvgaName: String = "tags_lucky.svga",
            onButtonClickListener: (() -> Unit)? = null,
            addAmount: String = "",
            remainingCount: Int = 0,
            treasureBoxImageUrl: String? = null,
            activityType: CountdownManager.ActivityType,
            activityId: String
        ): PromotionDialogFragment {
            return PromotionDialogFragment().apply {
                this.layoutResId = layoutResId
                this.amount = amount
                this.addAmount = addAmount
                this.description = description
                this.price = price
                this.oldPrice = originPrice
                this.buttonSvgaName = buttonSvgaName
                this.buttonEffectSvgaName = buttonEffectSvgaName
                this.onButtonClickListener = onButtonClickListener
                this.remainingCount = remainingCount
                this.treasureBoxImageUrl = treasureBoxImageUrl
                this.activityType = activityType
                this.activityId = activityId
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 禁止点击外部和返回键关闭
        isCancelable = false
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        dialog?.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog?.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        return inflater.inflate(layoutResId, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 设置弹窗大小
        dialog?.window?.setLayout(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )

        // 设置蒙层透明度
        dialog?.window?.setDimAmount(0.6f)

        // 初始化视图
        setupViews(view)

        // 加载SVGA动画
        loadSvgaAnimations(view)

        // 开始倒计时
        startCountdown(view)
    }

    private fun setupViews(view: View) {
        // 设置宝箱图片 - 支持网络图片加载
        val ivTreasureBox = view.findViewById<ImageView>(R.id.iv_treasure_box)
        ivTreasureBox?.let {
            //todo。strategyConfig?.activityCategoryImageUrl 为null
            if (!treasureBoxImageUrl.isNullOrEmpty()) {
                // 使用网络图片
                GlideUtils.load(
                    context = requireContext(),
                    url = treasureBoxImageUrl,
                    imageView = it,
                    placeholder = R.drawable.promotion_action_src,
                    error = R.drawable.promotion_action_src
                )
            } else {
                // 使用默认图片
                GlideUtils.load(
                    context = requireContext(),
                    url = R.drawable.promotion_box,
                    imageView = it
                )
            }
        }

        // 设置金额文本
        val tvAmount = view.findViewById<TextView>(R.id.tv_amount)
        tvAmount?.text = amount

        // 设置额外金额文本
        val tvAddAmount = view.findViewById<TextView>(R.id.tv_add_amount)
        tvAddAmount?.text = addAmount

        // 设置描述文本
        val tvDescription = view.findViewById<TextView>(R.id.tv_description)
        if (description.isEmpty()) {
            tvDescription.visibility = View.GONE
        } else {
            tvDescription.visibility = View.VISIBLE
            tvDescription?.text = description
        }


        // 设置价格文本
        val tvPrice = view.findViewById<TextView>(R.id.tv_price)
        tvPrice?.text = price.toString()

        val price_layout = view.findViewById<LinearLayout>(R.id.price_layout)
        price_layout.click {
            onButtonClickListener?.invoke()
            dismiss()
        }

        // 设置原价文本
        val tvOldPrice = view.findViewById<TextView>(R.id.tv_old_price)
        if (tvOldPrice != null) {
            if (oldPrice > 0 && oldPrice > price) {
                tvOldPrice.text = oldPrice.toString()
                tvOldPrice.paint.isStrikeThruText = true  // 添加划线效果
                tvOldPrice.visibility = View.VISIBLE
            } else {
                tvOldPrice.visibility = View.GONE
            }
        }

        // 设置按钮文字 - 适用于新用户促销弹窗布局
        val tvButtonText = view.findViewById<TextView>(R.id.tv_button_text)
        tvButtonText?.text = "Claim"

        // 设置剩余数量文本
        val tvAccountDown = view.findViewById<TextView>(R.id.tv_account_down)
        if (tvAccountDown != null) {
            val text = "Only $remainingCount left"
            val spannable = android.text.SpannableString(text)
            val startIndex = text.indexOf(remainingCount.toString())
            val endIndex = startIndex + remainingCount.toString().length
            spannable.setSpan(
                android.text.style.ForegroundColorSpan("#FFFEFF00".toColorInt()),
                startIndex,
                endIndex,
                android.text.Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            tvAccountDown.text = spannable
        }

        // 设置关闭按钮点击事件
        val ivClose = view.findViewById<ImageView>(R.id.iv_close)
        ivClose?.click {
            dismiss()
        }
    }

    private fun loadSvgaAnimations(view: View) {
        val parser = SVGAParser(requireContext())

        // 加载按钮动画
        val svgaButton = view.findViewById<AlphaSVGAImageView>(R.id.svga_button)
        svgaButton.click {
            onButtonClickListener?.invoke()
            dismiss()
        }
        CustomUtils.playSvga(svgaButton, buttonSvgaName)

        // 加载按钮特效动画
        val svgaButtonEffect = view.findViewById<AlphaSVGAImageView>(R.id.svga_button_effect)
        CustomUtils.playSvga(svgaButtonEffect, buttonEffectSvgaName)
    }

    private fun startCountdown(view: View) {
        val tvCountdown = view.findViewById<TextView>(R.id.tv_countdown)
        // 如果倒计时文本不存在，直接返回，这适用于新用户促销弹窗布局
        if (tvCountdown == null) {
            return
        }

        // 设置倒计时背景
        setupCountdownBackground(tvCountdown)

        // 使用CountdownManager启动倒计时
        startCountdownWithManager(tvCountdown)
    }

    /**
     * 设置倒计时背景
     */
    private fun setupCountdownBackground(tvCountdown: TextView) {
        // 设置倒计时背景
        val cornerRadiiPromotion = floatArrayOf(
            100f, 100f,     // 左上角 x,y 半径
            100f, 100f,   // 右上角 x,y 半径
            100f, 100f,   // 右下角 x,y 半径
            13f, 13f        // 左下角 x,y 半径
        )
        // 设置倒计时背景
        val cornerRadiiPromotion1 = floatArrayOf(
            100f, 100f,     // 左上角 x,y 半径
            100f, 100f,   // 右上角 x,y 半径
            13f, 13f,   // 右下角 x,y 半径
            100f, 100f        // 左下角 x,y 半径
        )

        val backgroundColor = "#ff391805".toColorInt()
        val countdownBackground: GradientDrawable = when (layoutResId) {
            R.layout.dialog_promotion ->
                DrawableUtils.createRoundRectDrawable(backgroundColor, cornerRadiiPromotion)
            R.layout.dialog_promotion1 ->
                DrawableUtils.createRoundRectDrawable(backgroundColor, cornerRadiiPromotion1)
            else -> return
        }

        tvCountdown.background = countdownBackground
    }

    /**
     * 使用CountdownManager启动倒计时
     */
    private fun startCountdownWithManager(tvCountdown: TextView) {
        try {
            // 取消之前的倒计时Job
            countdownJob?.cancel()

            // 移除之前的监听者
            if (onTickListener != null && onFinishListener != null) {
                CountdownManager.removeListener(activityType!!, activityId!!, onTickListener!!, onFinishListener!!)
            }

            // 从CountdownManager获取当前剩余时间
            val currentRemainingTime = CountdownManager.getCurrentRemainingTime(activityType!!, activityId!!)

            if (currentRemainingTime <= 0) {
                tvCountdown.text = "00:00:00"
                Timber.tag("PromotionDialog").d("活动已过期: $activityType, $activityId")
                return
            }

            // 创建监听者
            onTickListener = { remainingMillis ->
                ThreadUtils.runOnMain {
                    try {
                        if (isAdded && !isDetached && view != null) {
                            val remainingSeconds = remainingMillis / 1000
                            tvCountdown.text = TimeUtils.formatSecondsToTime(remainingSeconds)
                        }
                    } catch (e: Exception) {
                        Timber.tag("PromotionDialog").e(e, "更新倒计时UI失败")
                    }
                }
            }

            onFinishListener = {
                ThreadUtils.runOnMain {
                    try {
                        if (isAdded && !isDetached && view != null) {
                            tvCountdown.text = "00:00:00"
                        }
                    } catch (e: Exception) {
                        Timber.tag("PromotionDialog").e(e, "倒计时结束UI更新失败")
                    }
                }
            }

            // 使用CountdownManager启动倒计时
            countdownJob = CountdownManager.startCountdown(
                activityType = activityType!!,
                activityId = activityId!!,
                onTick = onTickListener!!,
                onFinish = onFinishListener!!
            )

            Timber.tag("PromotionDialog").d("使用CountdownManager启动倒计时: $activityType, $activityId, 剩余时间: ${currentRemainingTime}ms")
        } catch (e: Exception) {
            Timber.tag("PromotionDialog").e(e, "CountdownManager倒计时启动失败")
            tvCountdown.text = "00:00:00"
        }
    }



    override fun onDestroyView() {
        // 移除CountdownManager监听者
        if (activityType != null && !activityId.isNullOrEmpty() &&
            onTickListener != null && onFinishListener != null) {
            try {
                CountdownManager.removeListener(activityType!!, activityId!!, onTickListener!!, onFinishListener!!)
                Timber.tag("PromotionDialog").d("移除CountdownManager监听者: $activityType, $activityId")
            } catch (e: Exception) {
                Timber.tag("PromotionDialog").e(e, "移除CountdownManager监听者失败")
            }
        }

        // 清理资源
        countdownJob = null
        onTickListener = null
        onFinishListener = null

        Timber.tag("PromotionDialog").d("清理倒计时资源")
        super.onDestroyView()
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        // 通知父Fragment弹窗已关闭
        parentFragmentManager.setFragmentResult("PromotionDialog_dismissed", Bundle())
    }
}