package com.score.callmetest.util

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ImageSpan
import androidx.core.content.ContextCompat
import com.opensource.svgaplayer.SVGACallback
import com.opensource.svgaplayer.SVGADrawable
import com.opensource.svgaplayer.SVGAImageView
import com.opensource.svgaplayer.SVGAParser
import com.score.callmetest.R
import com.score.callmetest.db.DatabaseFactory
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.ui.login.LoginActivity
import timber.log.Timber
import java.math.BigDecimal
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * 当前项目抽离出公用的一些功能
 */
object CustomUtils {
    private const val TAG = "CustomUtils"

    /**
     *  播放一次svga
     *
     * @param [svgaView] view
     * @param [assetName] svga-name
     */
    fun playSvgaOnce(svgaView: SVGAImageView, assetName: String?) {
        playSvga(svgaView, assetName, 1)
    }

    /**
     *  播放svga
     *
     * @param [svgaView] view
     * @param [assetName] svga-name
     * @param [loops] 播放次数--默认无限
     */
    fun playSvga(
        svgaView: SVGAImageView,
        assetName: String?,
        loops: Int = 0,
        onFinished: (() -> Unit)? = null,
        onRepeat: (() -> Unit)? = null,
    ) {
        if (assetName == null) return

        ThreadUtils.runOnIO {
            SVGAParser.shareParser().apply {
                init(svgaView.context)
                decodeFromAssets(assetName, object : SVGAParser.ParseCompletion {
                    override fun onComplete(videoItem: com.opensource.svgaplayer.SVGAVideoEntity) {
                        ThreadUtils.runOnMain {
                            // 检查 View 是否还有效
                            if (svgaView.context == null) {
                                return@runOnMain
                            }

                            svgaView.setImageDrawable(SVGADrawable(videoItem))
                            svgaView.loops = loops
                            svgaView.startAnimation()
                            svgaView.callback = object : SVGACallback {
                                override fun onFinished() {
                                    onFinished?.invoke()
                                }

                                override fun onPause() {
                                }

                                override fun onRepeat() {
                                    onRepeat?.invoke()
                                }

                                override fun onStep(frame: Int, percentage: Double) {

                                }
                            }
                        }
                    }

                    override fun onError() {}
                })
            }
        }
    }

    /**
     * 退出登录时清理所有本地数据、SDK资源，并跳转到登录页
     * @param [type] 类型  1-清理
     *                     0-正常退出
     */
    fun logoutAndClearData(type: Int = 0, context: Context) {
        GlobalManager.onLogout() // 重置全局状态 声网SDK登出
        //type == 1 注销账号，type == 0 登出
        if (type == 1) {
            GlobalManager.deleteUser()
            // 用协程在IO线程清空数据库，清理完成后再跳转
            ThreadUtils.runOnIO {
                try {
                    // 清空当前用户数据
                    DatabaseFactory.getDatabase(context).clearCurrentUserDatas()
                } catch (e: Exception) {
                    Timber.tag(TAG).e("删除数据库失败： ${e}")
                }
                gotoLogin(context)
            }
        } else if (type == 0) {

            gotoLogin(context)
        }
        else {
            Timber.tag(TAG).w("退出登录类型错误")
        }
    }

    /**
     * 转到登录
     * @param [context] 上下文
     */
    fun gotoLogin(context: Context) {
        if (ActivityUtils.getTopActivity() is LoginActivity) return
        val intent = Intent(context, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        context.startActivity(intent)
        if (context is Activity) {
            context.finish()
        }
    }

    /**
     * 计算字符串的显示长度
     * 数字/字母/符号 = 1个字符，汉字 = 2个字符
     *
     * @param text 要计算的字符串
     * @return 显示长度
     */
    fun calculateDisplayLength(text: String): Int {
        var length = 0
        for (char in text) {
            // 判断是否为中文字符（包括中文标点符号）
            if (isChinese(char)) {
                length += 2
            } else {
                length += 1
            }
        }
        return length
    }

    /**
     * 判断字符是否为中文字符
     *
     * @param char 要判断的字符
     * @return true表示是中文字符，false表示不是
     */
    private fun isChinese(char: Char): Boolean {
        val code = char.code
        // 中文字符的Unicode范围
        return (code in 0x4E00..0x9FFF) ||  // 基本汉字
                (code in 0x3400..0x4DBF) ||  // 扩展A
                (code in 0x20000..0x2A6DF) || // 扩展B
                (code in 0x2A700..0x2B73F) || // 扩展C
                (code in 0x2B740..0x2B81F) || // 扩展D
                (code in 0x2B820..0x2CEAF) || // 扩展E
                (code in 0x2CEB0..0x2EBEF) || // 扩展F
                (code in 0x30000..0x3134F) || // 扩展G
                (code in 0x3000..0x303F) ||   // 中文标点符号
                (code in 0xFF00..0xFFEF)      // 全角字符
    }

    /**
     * 检查字符串的显示长度是否超过限制
     *
     * @param text 要检查的字符串
     * @param maxDisplayLength 最大显示长度限制
     * @return true表示超过限制，false表示未超过
     */
    fun isDisplayLengthExceeded(text: String, maxDisplayLength: Int): Boolean {
        return calculateDisplayLength(text) > maxDisplayLength
    }

    /**
     * 截取字符串到指定的显示长度
     *
     * @param text 原始字符串
     * @param maxDisplayLength 最大显示长度
     * @return 截取后的字符串
     */
    fun truncateToDisplayLength(text: String, maxDisplayLength: Int): String {
        var currentLength = 0
        val result = StringBuilder()

        for (char in text) {
            val charLength = if (isChinese(char)) 2 else 1
            if (currentLength + charLength > maxDisplayLength) {
                break
            }
            result.append(char)
            currentLength += charLength
        }

        return result.toString()
    }

    /**
     * 根据礼物名称获取对应的资源ID
     *
     * @param giftCode 礼物code
     * @return 资源ID
     */
    fun getGiftResIdById(giftCode: String?): Int {
        // 这里实现根据礼物名称查找资源ID的逻辑
        return when (giftCode) {
            "1" -> R.drawable.kiss  // 文档香蕉、kiss、青瓜都有。。
            "2" -> R.drawable.meigui // 一支玫瑰、巧克力
            "3" -> R.drawable.bear // 熊
            "4" -> R.drawable.lipstick // 唇膏
            "5" -> R.drawable.xiangbin // 香槟、香水
            "6" -> R.drawable.rose // 一束玫瑰
            "7" -> R.drawable.shouzhuo  // 表耳环手镯--我选手镯
            "8" -> R.drawable.crystal_shoes // 水晶鞋
            "9" -> R.drawable.yongyi // 泳衣
            "10" -> R.drawable.hunsha // 婚纱
            "11" -> R.drawable.aimashibao // 爱马仕包
            "12" -> R.drawable.ring // 戒指
            "13" -> R.drawable.chuang // 床
            "14" -> R.drawable.xianglian // 砖石项链
            "15" -> R.drawable.huangguan // 皇冠
            "16" -> R.drawable.paoche // 跑车
            "17" -> R.drawable.bieshu // 别墅
            "18" -> R.drawable.feiji // 飞机
            "19" -> R.drawable.youlun // 油轮
            "20" -> R.drawable.chengbao // 城堡
            else -> R.drawable.gift_placehold
        }
    }

    /**
     * 创建带金币图标的SpannableString - 完整版本
     *
     * @param context 上下文
     * @param text 原始文本，使用"icon"作为占位符，或者使用insertPosition指定插入位置
     * @param coinSizeDp 金币图标大小（dp），默认14dp
     * @param alignment 图标对齐方式，默认ALIGN_BOTTOM
     * @param spacesBefore 金币图标前的空格数量
     * @param spacesAfter 金币图标后的空格数量
     * @param insertPosition 指定插入位置（可选），如果为null则查找"icon"占位符
     * @return 包含金币图标的SpannableString
     */
    fun createCoinSpannableText(
        context: Context,
        text: String,
        coinSizeDp: Float = 14f,
        alignment: Int = ImageSpan.ALIGN_BOTTOM,
        spacesBefore: Int = 0,
        spacesAfter: Int = 0,
        insertPosition: Int? = null
    ): SpannableString {
        // 构建最终文本，添加前后空格
        val spacesBeforeStr = " ".repeat(spacesBefore)
        val spacesAfterStr = " ".repeat(spacesAfter)

        val finalText: String
        val iconPosition: Int

        if (insertPosition != null) {
            // 使用指定位置插入
            finalText = text.substring(0, insertPosition) +
                       spacesBeforeStr + " " + spacesAfterStr +
                       text.substring(insertPosition)
            iconPosition = insertPosition + spacesBefore
        } else {
            // 查找并替换"icon"占位符
            val iconIndex = text.indexOf("icon")
            if (iconIndex == -1) {
                // 如果没有找到"icon"，直接返回原文本
                return SpannableString(text)
            }

            finalText = text.replace("icon", spacesBeforeStr + " " + spacesAfterStr)
            iconPosition = iconIndex + spacesBefore
        }

        val spannable = SpannableString(finalText)

        // 获取金币图标drawable
        val coinDrawable = ContextCompat.getDrawable(context, R.drawable.coin)
        coinDrawable?.let { drawable ->
            // 设置图标大小
            val iconSize = DisplayUtils.dp2px(coinSizeDp)
            drawable.setBounds(0, 0, iconSize, iconSize)

            // 创建ImageSpan并插入到指定位置
            val imageSpan = ImageSpan(drawable, alignment)
            spannable.setSpan(
                imageSpan,
                iconPosition,
                iconPosition + 1,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        return spannable
    }

    /**
     * 比较价格
     * @param [price1] price1
     * @param [price2] price2
     * @return [Int]
     */
    fun comparePrice(price1: String, price2: String): Int {
        // 1. 移除所有非数字字符（保留小数点）
        val clean1 = price1.filter { it.isDigit() || it == '.' }
        val clean2 = price2.filter { it.isDigit() || it == '.' }

        // 2. 转换为 BigDecimal（避免浮点精度问题）
        val price1 = clean1.toBigDecimalOrNull() ?: BigDecimal.ZERO
        val price2 = clean2.toBigDecimalOrNull() ?: BigDecimal.ZERO

        // 3. 返回标准比较结果（-1, 0, 1）
        return price1.compareTo(price2)
    }

}