package com.score.callmetest.util

import android.util.Log
import android.view.View
import android.view.View.OnClickListener
import com.score.callmetest.R

/**
 * 扩展函数：将字符串作为日志输出，自动处理超长日志的分段
 * 
 * @param tag 日志标签
 * @param priority 日志优先级，默认为Log.DEBUG
 */
fun String.logAsTag(tag: String, priority: Int = Log.DEBUG) {
    val maxLength = 4000
    val bytes = this.toByteArray(Charsets.UTF_8)
    if (bytes.size <= maxLength) {
        when (priority) {
            Log.VERBOSE -> Log.v(tag, this)
            Log.DEBUG -> Log.d(tag, this)
            Log.INFO -> Log.i(tag, this)
            Log.WARN -> Log.w(tag, this)
            Log.ERROR -> Log.e(tag, this)
            else -> Log.d(tag, this)
        }
        return
    }

    var start = 0
    var index = 0
    while (start < bytes.size) {
        val end = (start + maxLength).coerceAtMost(bytes.size)
        // 处理多字节字符边界，避免截断
        var realEnd = end
        if (end < bytes.size) {
            // 向前回溯，直到找到合法的UTF-8起始字节
            while (realEnd > start && (bytes[realEnd].toInt() and 0xC0) == 0x80) {
                realEnd--
            }
            if (realEnd == start) realEnd = end // fallback，极端情况
        }
        val segment = bytes.copyOfRange(start, realEnd).toString(Charsets.UTF_8)
        val segmentTag = if (index == 0) tag else "$tag[$index]"
        when (priority) {
            Log.VERBOSE -> Log.v(segmentTag, segment)
            Log.DEBUG -> Log.d(segmentTag, segment)
            Log.INFO -> Log.i(segmentTag, segment)
            Log.WARN -> Log.w(segmentTag, segment)
            Log.ERROR -> Log.e(segmentTag, segment)
            else -> Log.d(segmentTag, segment)
        }
        start = realEnd
        index++
    }
}

/**
 * 扩展函数：输出调试日志
 */
fun String.logD(tag: String) = this.logAsTag(tag, Log.DEBUG)

/**
 * 扩展函数：输出信息日志
 */
fun String.logI(tag: String) = this.logAsTag(tag, Log.INFO)

/**
 * 扩展函数：输出警告日志
 */
fun String.logW(tag: String) = this.logAsTag(tag, Log.WARN)

/**
 * 扩展函数：输出错误日志
 */
fun String.logE(tag: String) = this.logAsTag(tag, Log.ERROR)

fun View.click(interval: Long = 1500, action: (View) -> Unit) {
    this.setOnClickListener {  v ->
        val now = System.currentTimeMillis()
        val lastTime = v.getTag(R.id.click_time_tag) as? Long ?: 0L
        if (now - lastTime > interval) {
            v.setTag(R.id.click_time_tag, now)
            action(v)
        }
    }
}
